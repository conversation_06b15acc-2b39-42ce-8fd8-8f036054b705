# Room Entity for AutoCAD 2024

A comprehensive custom AutoCAD entity implementation for room management with advanced geometric editing, property management, and user interface integration.

## Project Structure

- **RoomEntityCpp**: C++ ObjectARX implementation of the core Room entity
- **RoomEntityManaged**: C# managed wrapper for .NET integration
- **RoomEntityUI**: Windows Forms dialogs and user interface components

## Prerequisites

- Visual Studio 2022
- AutoCAD 2024 (installed at: C:\Program Files\Autodesk\AutoCAD 2024)
- ObjectARX SDK 2024 (installed at: C:\Autodesk\ObjectARX_for_AutoCAD_2024_Win_64bit_dlm)
- .NET Framework 4.8

## Environment Setup

Ensure the following environment variables are set:

```powershell
$env:ARXSDK_2024 = 'C:\Autodesk\ObjectARX_for_AutoCAD_2024_Win_64bit_dlm'
$env:ACAD_2024 = 'C:\Program Files\Autodesk\AutoCAD 2024'
```

## Features

### Core Entity Properties
- **Room Name**: String property for room identification
- **Room Number**: String/Integer property for numbering system
- **Area**: Automatically calculated from geometric area (in mm²)
- **Ceiling Height**: Double property for room height specification
- **Windows and Doors**: Collection of geometric symbols

### Geometric Behavior
- Initial rectangular shape with grip editing support
- Polygon transformation capabilities
- Real-time area calculation
- Grip points for vertex manipulation

### User Interface
- Property labels with visibility toggle
- Right-click context menu
- Double-click property editing dialog
- AutoCAD Properties palette integration
- Grip editing with visual feedback

### Technical Features
- Full ObjectARX integration
- C# managed wrapper with strong typing
- AutoCAD 2024 compatibility
- Millimeter units by default
- Simplified line-based window/door symbols

## Building the Project

1. Open `RoomEntity.sln` in Visual Studio 2022
2. Ensure all paths in project files match your installation
3. Build in x64 configuration (Debug or Release)
4. The output will be generated in the `bin\` directory

## Installation

1. Build the solution
2. Copy the generated `.arx` file to AutoCAD's application directory
3. Load the application using `NETLOAD` command in AutoCAD
4. Use the `CREATEROOM` command to start creating room entities

## Usage

### Creating a Room
```
Command: CREATEROOM
```

### Editing Properties
- Double-click the room entity to open the property dialog
- Use the Properties palette for direct property modification
- Right-click for context menu options

### Geometric Editing
- Select the room entity to display grip points
- Drag grip points to modify the room shape
- Area is automatically recalculated

## Development Notes

- The project uses ObjectARX 2024 APIs
- All measurements are in millimeters
- The entity supports full AutoCAD serialization
- Grip editing maintains geometric integrity
- Property changes trigger automatic updates
