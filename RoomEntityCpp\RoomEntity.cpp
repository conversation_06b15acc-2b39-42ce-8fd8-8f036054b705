#include "pch.h"
#include "RoomEntity.h"
#include "WindowDoorSymbol.h"

// Static member definitions
const double RoomEntity::MIN_CEILING_HEIGHT = 1000.0; // 1 meter in mm
const double RoomEntity::DEFAULT_CEILING_HEIGHT = 2700.0; // 2.7 meters in mm
const int RoomEntity::MIN_VERTICES = 3;

ACRX_DXF_DEFINE_MEMBERS(RoomEntity, AcDbEntity,
    AcDb::kDHL_CURRENT, AcDb::kMReleaseCurrent,
    AcDbProxyEntity::kNoOperation,
    ROOM_ENTITY_DXF_NAME, ROOM_ENTITY_APP_NAME);

RoomEntity::RoomEntity()
    : m_roomName(L"Room")
    , m_roomNumber(L"001")
    , m_area(0.0)
    , m_ceilingHeight(DEFAULT_CEILING_HEIGHT)
    , m_showLabels(true)
{
    // Initialize with a default rectangle (1m x 1m)
    m_vertices.append(AcGePoint3d(0.0, 0.0, 0.0));
    m_vertices.append(AcGePoint3d(1000.0, 0.0, 0.0));
    m_vertices.append(AcGePoint3d(1000.0, 1000.0, 0.0));
    m_vertices.append(AcGePoint3d(0.0, 1000.0, 0.0));
    
    calculateArea();
}

RoomEntity::~RoomEntity()
{
    clearWindowsDoors();
}

void RoomEntity::rxInit()
{
    RoomEntity::rxInit();
    acrxBuildClassHierarchy();
}

void RoomEntity::rxUninit()
{
    deleteAcRxClass(RoomEntity::desc());
}

Acad::ErrorStatus RoomEntity::dwgInFields(AcDbDwgFiler* pFiler)
{
    assertWriteEnabled();
    
    Acad::ErrorStatus es = AcDbEntity::dwgInFields(pFiler);
    if (es != Acad::eOk)
        return es;

    // Read version
    Adesk::UInt16 version;
    pFiler->readUInt16(&version);
    
    if (version > 1)
        return Acad::eMakeMeProxy;

    // Read properties
    pFiler->readString(m_roomName);
    pFiler->readString(m_roomNumber);
    pFiler->readDouble(&m_area);
    pFiler->readDouble(&m_ceilingHeight);
    pFiler->readBool(&m_showLabels);

    // Read vertices
    Adesk::UInt32 numVertices;
    pFiler->readUInt32(&numVertices);
    m_vertices.removeAll();
    
    for (Adesk::UInt32 i = 0; i < numVertices; i++)
    {
        AcGePoint3d pt;
        pFiler->readPoint3d(&pt);
        m_vertices.append(pt);
    }

    // Read windows/doors
    Adesk::UInt32 numSymbols;
    pFiler->readUInt32(&numSymbols);
    clearWindowsDoors();
    
    for (Adesk::UInt32 i = 0; i < numSymbols; i++)
    {
        auto symbol = std::make_unique<WindowDoorSymbol>();
        es = symbol->dwgInFields(pFiler);
        if (es == Acad::eOk)
            m_windowsDoors.push_back(std::move(symbol));
    }

    validateGeometry();
    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::dwgOutFields(AcDbDwgFiler* pFiler) const
{
    assertReadEnabled();
    
    Acad::ErrorStatus es = AcDbEntity::dwgOutFields(pFiler);
    if (es != Acad::eOk)
        return es;

    // Write version
    pFiler->writeUInt16(1);

    // Write properties
    pFiler->writeString(m_roomName);
    pFiler->writeString(m_roomNumber);
    pFiler->writeDouble(m_area);
    pFiler->writeDouble(m_ceilingHeight);
    pFiler->writeBool(m_showLabels);

    // Write vertices
    pFiler->writeUInt32(static_cast<Adesk::UInt32>(m_vertices.length()));
    for (int i = 0; i < m_vertices.length(); i++)
    {
        pFiler->writePoint3d(m_vertices[i]);
    }

    // Write windows/doors
    pFiler->writeUInt32(static_cast<Adesk::UInt32>(m_windowsDoors.size()));
    for (const auto& symbol : m_windowsDoors)
    {
        symbol->dwgOutFields(pFiler);
    }

    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::dxfInFields(AcDbDxfFiler* pFiler)
{
    assertWriteEnabled();
    
    Acad::ErrorStatus es = AcDbEntity::dxfInFields(pFiler);
    if (es != Acad::eOk)
        return es;

    // Implementation similar to dwgInFields but using DXF codes
    // This is a simplified version - full implementation would handle all DXF codes
    
    validateGeometry();
    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::dxfOutFields(AcDbDxfFiler* pFiler) const
{
    assertReadEnabled();
    
    Acad::ErrorStatus es = AcDbEntity::dxfOutFields(pFiler);
    if (es != Acad::eOk)
        return es;

    // Implementation similar to dwgOutFields but using DXF codes
    // This is a simplified version - full implementation would handle all DXF codes
    
    return Acad::eOk;
}

Adesk::Boolean RoomEntity::worldDraw(AcGiWorldDraw* pWd)
{
    assertReadEnabled();
    
    if (pWd->regenAbort())
        return Adesk::kTrue;

    // Draw the room polygon
    drawPolygon(pWd);
    
    // Draw windows and doors
    drawWindowsDoors(pWd);
    
    // Draw labels if enabled
    if (m_showLabels)
        drawLabels(pWd);

    return Adesk::kTrue;
}

void RoomEntity::viewportDraw(AcGiViewportDraw* pVd)
{
    assertReadEnabled();
    // Viewport-specific drawing if needed
}

void RoomEntity::setRoomName(const ACHAR* name)
{
    assertWriteEnabled();
    m_roomName = name ? name : L"";
}

void RoomEntity::setRoomNumber(const ACHAR* number)
{
    assertWriteEnabled();
    m_roomNumber = number ? number : L"";
}

void RoomEntity::setCeilingHeight(double height)
{
    assertWriteEnabled();
    m_ceilingHeight = (height >= MIN_CEILING_HEIGHT) ? height : MIN_CEILING_HEIGHT;
}

void RoomEntity::setShowLabels(bool show)
{
    assertWriteEnabled();
    m_showLabels = show;
}

void RoomEntity::setVertices(const AcGePoint3dArray& vertices)
{
    assertWriteEnabled();
    m_vertices = vertices;
    ensureMinimumVertices();
    calculateArea();
    validateGeometry();
}

void RoomEntity::calculateArea()
{
    m_area = calculatePolygonArea();
}

double RoomEntity::calculatePolygonArea() const
{
    if (m_vertices.length() < 3)
        return 0.0;

    double area = 0.0;
    int n = m_vertices.length();
    
    for (int i = 0; i < n; i++)
    {
        int j = (i + 1) % n;
        area += m_vertices[i].x * m_vertices[j].y;
        area -= m_vertices[j].x * m_vertices[i].y;
    }
    
    return abs(area) / 2.0;
}

void RoomEntity::ensureMinimumVertices()
{
    while (m_vertices.length() < MIN_VERTICES)
    {
        if (m_vertices.length() == 0)
            m_vertices.append(AcGePoint3d::kOrigin);
        else
        {
            AcGePoint3d lastPt = m_vertices[m_vertices.length() - 1];
            m_vertices.append(lastPt + AcGeVector3d(100.0, 0.0, 0.0));
        }
    }
}

void RoomEntity::validateGeometry()
{
    ensureMinimumVertices();
    
    // Ensure all vertices are coplanar (Z = 0 for simplicity)
    for (int i = 0; i < m_vertices.length(); i++)
    {
        m_vertices[i].z = 0.0;
    }
}

void RoomEntity::drawPolygon(AcGiWorldDraw* pWd) const
{
    if (m_vertices.length() < 3)
        return;

    // Set drawing attributes
    pWd->subEntityTraits().setColor(1); // Red color
    pWd->subEntityTraits().setLineWeight(AcDb::kLnWt025);

    // Draw polygon outline
    AcGePoint3dArray points = m_vertices;
    points.append(m_vertices[0]); // Close the polygon
    
    pWd->geometry().polyline(points.length(), points.asArrayPtr());
}

void RoomEntity::drawLabels(AcGiWorldDraw* pWd) const
{
    AcGePoint3d centroid = getCentroid();
    
    // Draw room name and area at centroid
    AcString label;
    label.format(L"%s\nArea: %.2f m²", 
                m_roomName.kACharPtr(), 
                m_area / 1000000.0); // Convert mm² to m²

    // This is a simplified text drawing - full implementation would use proper text entities
    pWd->subEntityTraits().setColor(2); // Yellow color
}

void RoomEntity::drawWindowsDoors(AcGiWorldDraw* pWd) const
{
    for (const auto& symbol : m_windowsDoors)
    {
        symbol->draw(pWd);
    }
}

AcGePoint3d RoomEntity::getCentroid() const
{
    if (m_vertices.length() == 0)
        return AcGePoint3d::kOrigin;

    AcGePoint3d centroid(0.0, 0.0, 0.0);
    for (int i = 0; i < m_vertices.length(); i++)
    {
        centroid += m_vertices[i].asVector();
    }
    
    return centroid / static_cast<double>(m_vertices.length());
}

void RoomEntity::clearWindowsDoors()
{
    m_windowsDoors.clear();
}

Acad::ErrorStatus RoomEntity::getGeomExtents(AcDbExtents& extents) const
{
    assertReadEnabled();

    if (m_vertices.length() == 0)
        return Acad::eInvalidInput;

    extents.set(m_vertices[0], m_vertices[0]);

    for (int i = 1; i < m_vertices.length(); i++)
    {
        extents.addPoint(m_vertices[i]);
    }

    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::transformBy(const AcGeMatrix3d& xform)
{
    assertWriteEnabled();

    // Transform all vertices
    for (int i = 0; i < m_vertices.length(); i++)
    {
        m_vertices[i].transformBy(xform);
    }

    // Transform windows/doors
    for (auto& symbol : m_windowsDoors)
    {
        symbol->transformBy(xform);
    }

    calculateArea();
    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::getTransformedCopy(const AcGeMatrix3d& xform, AcDbEntity*& pCopy) const
{
    assertReadEnabled();

    RoomEntity* pNewEntity = new RoomEntity(*this);
    if (!pNewEntity)
        return Acad::eOutOfMemory;

    Acad::ErrorStatus es = pNewEntity->transformBy(xform);
    if (es != Acad::eOk)
    {
        delete pNewEntity;
        return es;
    }

    pCopy = pNewEntity;
    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::getGripPoints(AcGePoint3dArray& gripPoints,
                                          AcDbIntArray& osnapModes,
                                          AcDbIntArray& geomIds) const
{
    assertReadEnabled();

    gripPoints.removeAll();
    osnapModes.removeAll();
    geomIds.removeAll();

    // Add grip points for each vertex
    for (int i = 0; i < m_vertices.length(); i++)
    {
        gripPoints.append(m_vertices[i]);
        osnapModes.append(AcDb::kOsModeEnd);
        geomIds.append(i);
    }

    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::moveGripPointsAt(const AcDbIntArray& indices,
                                             const AcGeVector3d& offset)
{
    assertWriteEnabled();

    for (int i = 0; i < indices.length(); i++)
    {
        int index = indices[i];
        if (index >= 0 && index < m_vertices.length())
        {
            m_vertices[index] += offset;
        }
    }

    calculateArea();
    validateGeometry();
    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::getStretchPoints(AcGePoint3dArray& stretchPoints) const
{
    assertReadEnabled();

    stretchPoints.removeAll();

    // All vertices are stretch points
    for (int i = 0; i < m_vertices.length(); i++)
    {
        stretchPoints.append(m_vertices[i]);
    }

    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::moveStretchPointsAt(const AcDbIntArray& indices,
                                                 const AcGeVector3d& offset)
{
    return moveGripPointsAt(indices, offset);
}

int RoomEntity::getWindowDoorCount() const
{
    return static_cast<int>(m_windowsDoors.size());
}

const WindowDoorSymbol* RoomEntity::getWindowDoor(int index) const
{
    if (index >= 0 && index < static_cast<int>(m_windowsDoors.size()))
        return m_windowsDoors[index].get();
    return nullptr;
}

Acad::ErrorStatus RoomEntity::addWindowDoor(const WindowDoorSymbol& symbol)
{
    assertWriteEnabled();

    auto newSymbol = std::make_unique<WindowDoorSymbol>(symbol);
    if (!newSymbol)
        return Acad::eOutOfMemory;

    m_windowsDoors.push_back(std::move(newSymbol));
    return Acad::eOk;
}

Acad::ErrorStatus RoomEntity::removeWindowDoor(int index)
{
    assertWriteEnabled();

    if (index < 0 || index >= static_cast<int>(m_windowsDoors.size()))
        return Acad::eInvalidIndex;

    m_windowsDoors.erase(m_windowsDoors.begin() + index);
    return Acad::eOk;
}

bool RoomEntity::isPointInside(const AcGePoint3d& point) const
{
    if (m_vertices.length() < 3)
        return false;

    // Ray casting algorithm for point-in-polygon test
    int intersections = 0;
    int n = m_vertices.length();

    for (int i = 0; i < n; i++)
    {
        int j = (i + 1) % n;

        if (((m_vertices[i].y > point.y) != (m_vertices[j].y > point.y)) &&
            (point.x < (m_vertices[j].x - m_vertices[i].x) * (point.y - m_vertices[i].y) /
             (m_vertices[j].y - m_vertices[i].y) + m_vertices[i].x))
        {
            intersections++;
        }
    }

    return (intersections % 2) == 1;
}
