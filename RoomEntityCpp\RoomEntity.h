#pragma once
#include "pch.h"

// Forward declarations
class WindowDoorSymbol;

class ROOMENTITY_API RoomEntity : public AcDbEntity
{
public:
    ACRX_DECLARE_MEMBERS(RoomEntity);

    // Constructors and destructor
    RoomEntity();
    virtual ~RoomEntity();

    // AcDbEntity overrides
    virtual Acad::ErrorStatus dwgInFields(AcDbDwgFiler* pFiler) override;
    virtual Acad::ErrorStatus dwgOutFields(AcDbDwgFiler* pFiler) override;
    virtual Acad::ErrorStatus dxfInFields(AcDbDxfFiler* pFiler) override;
    virtual Acad::ErrorStatus dxfOutFields(AcDbDxfFiler* pFiler) override;

    // Drawing and display
    virtual Adesk::Boolean worldDraw(AcGiWorldDraw* pWd) override;
    virtual void viewportDraw(AcGiViewportDraw* pVd) override;

    // Geometric queries
    virtual Acad::ErrorStatus getGeomExtents(AcDbExtents& extents) const override;
    virtual Acad::ErrorStatus transformBy(const AcGeMatrix3d& xform) override;
    virtual Acad::ErrorStatus getTransformedCopy(const AcGeMatrix3d& xform, AcDbEntity*& pCopy) const override;

    // Grip handling
    virtual Acad::ErrorStatus getGripPoints(AcGePoint3dArray& gripPoints, 
                                          AcDbIntArray& osnapModes, 
                                          AcDbIntArray& geomIds) const override;
    virtual Acad::ErrorStatus moveGripPointsAt(const AcDbIntArray& indices, 
                                             const AcGeVector3d& offset) override;
    virtual Acad::ErrorStatus getStretchPoints(AcGePoint3dArray& stretchPoints) const override;
    virtual Acad::ErrorStatus moveStretchPointsAt(const AcDbIntArray& indices, 
                                                 const AcGeVector3d& offset) override;

    // Property access methods
    const ACHAR* getRoomName() const { return m_roomName.kACharPtr(); }
    void setRoomName(const ACHAR* name);

    const ACHAR* getRoomNumber() const { return m_roomNumber.kACharPtr(); }
    void setRoomNumber(const ACHAR* number);

    double getArea() const { return m_area; }
    void setArea(double area) { m_area = area; }

    double getCeilingHeight() const { return m_ceilingHeight; }
    void setCeilingHeight(double height);

    bool getShowLabels() const { return m_showLabels; }
    void setShowLabels(bool show);

    // Geometric data access
    const AcGePoint3dArray& getVertices() const { return m_vertices; }
    void setVertices(const AcGePoint3dArray& vertices);

    // Window/Door management
    int getWindowDoorCount() const;
    const WindowDoorSymbol* getWindowDoor(int index) const;
    Acad::ErrorStatus addWindowDoor(const WindowDoorSymbol& symbol);
    Acad::ErrorStatus removeWindowDoor(int index);
    void clearWindowsDoors();

    // Utility methods
    void calculateArea();
    bool isPointInside(const AcGePoint3d& point) const;
    AcGePoint3d getCentroid() const;

    // Static methods for entity registration
    static void rxInit();
    static void rxUninit();

private:
    // Property data
    AcString m_roomName;
    AcString m_roomNumber;
    double m_area;
    double m_ceilingHeight;
    bool m_showLabels;

    // Geometric data
    AcGePoint3dArray m_vertices;
    
    // Window/Door symbols
    std::vector<std::unique_ptr<WindowDoorSymbol>> m_windowsDoors;

    // Internal methods
    void drawPolygon(AcGiWorldDraw* pWd) const;
    void drawLabels(AcGiWorldDraw* pWd) const;
    void drawWindowsDoors(AcGiWorldDraw* pWd) const;
    double calculatePolygonArea() const;
    void ensureMinimumVertices();
    void validateGeometry();

    // Constants
    static const double MIN_CEILING_HEIGHT;
    static const double DEFAULT_CEILING_HEIGHT;
    static const int MIN_VERTICES;
};

// Smart pointer typedef for convenience
typedef AcDbObjectPointer<RoomEntity> RoomEntityPtr;
