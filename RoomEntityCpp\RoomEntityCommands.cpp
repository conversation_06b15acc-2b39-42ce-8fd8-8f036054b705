#include "pch.h"
#include "RoomEntityCommands.h"
#include "RoomEntity.h"
#include "WindowDoorSymbol.h"

const ACHAR* RoomEntityCommands::COMMAND_GROUP = L"ROOMENTITY_COMMANDS";

void RoomEntityCommands::registerCommands()
{
    acedRegCmds->addCommand(COMMAND_GROUP, L"CREATEROOM", L"CREATEROOM", 
                           ACRX_CMD_MODAL, createRoomCommand);
    
    acedRegCmds->addCommand(COMMAND_GROUP, L"EDITROOM", L"EDITROOM", 
                           ACRX_CMD_MODAL, editRoomCommand);
    
    acedRegCmds->addCommand(COMMAND_GROUP, L"ADDWINDOW", L"ADDWINDOW", 
                           ACRX_CMD_MODAL, addWindowCommand);
    
    acedRegCmds->addCommand(COMMAND_GROUP, L"ADDDOOR", L"ADDDOOR", 
                           ACRX_CMD_MODAL, addDoorCommand);
    
    acedRegCmds->addCommand(COMMAN<PERSON>_GROUP, L"TOGGLELABELS", L"TOGGLELABELS", 
                           ACRX_CMD_MODAL, toggleLabelsCommand);
    
    acedRegCmds->addCommand(COMMAND_GROUP, L"ROOMAREA", L"ROOMAREA", 
                           ACRX_CMD_MODAL, calculateAreaCommand);
    
    acedRegCmds->addCommand(COMMAND_GROUP, L"ROOMPROPS", L"ROOMPROPS", 
                           ACRX_CMD_MODAL, roomPropertiesCommand);
}

void RoomEntityCommands::unregisterCommands()
{
    acedRegCmds->removeGroup(COMMAND_GROUP);
}

void RoomEntityCommands::createRoomCommand()
{
    acutPrintf(L"\nCreate Room Entity\n");
    
    // Get room properties from user
    AcString roomName, roomNumber;
    double ceilingHeight;
    promptForRoomProperties(roomName, roomNumber, ceilingHeight);
    
    // Get polygon points
    AcGePoint3dArray points;
    if (!getPolygonPoints(points))
    {
        acutPrintf(L"\nCommand cancelled.\n");
        return;
    }
    
    // Create the room entity
    RoomEntity* pRoom = new RoomEntity();
    if (!pRoom)
    {
        acutPrintf(L"\nError: Failed to create room entity.\n");
        return;
    }
    
    // Set properties
    pRoom->setRoomName(roomName.kACharPtr());
    pRoom->setRoomNumber(roomNumber.kACharPtr());
    pRoom->setCeilingHeight(ceilingHeight);
    pRoom->setVertices(points);
    
    // Add to database
    AcDbBlockTable* pBlockTable;
    AcDbBlockTableRecord* pBlockTableRecord;
    
    acdbHostApplicationServices()->workingDatabase()->getBlockTable(pBlockTable, AcDb::kForRead);
    pBlockTable->getAt(ACDB_MODEL_SPACE, pBlockTableRecord, AcDb::kForWrite);
    pBlockTable->close();
    
    AcDbObjectId entityId;
    pBlockTableRecord->appendAcDbEntity(entityId, pRoom);
    pBlockTableRecord->close();
    pRoom->close();
    
    acutPrintf(L"\nRoom entity created successfully.\n");
}

void RoomEntityCommands::editRoomCommand()
{
    acutPrintf(L"\nEdit Room Entity\n");
    
    AcDbObjectId entityId;
    if (!selectRoomEntity(entityId))
    {
        acutPrintf(L"\nNo room entity selected.\n");
        return;
    }
    
    RoomEntity* pRoom;
    if (!getRoomEntityForEdit(entityId, pRoom))
    {
        acutPrintf(L"\nError: Failed to open room entity for editing.\n");
        return;
    }
    
    // Display current properties
    acutPrintf(L"\nCurrent properties:\n");
    acutPrintf(L"Name: %s\n", pRoom->getRoomName());
    acutPrintf(L"Number: %s\n", pRoom->getRoomNumber());
    acutPrintf(L"Area: %.2f m²\n", pRoom->getArea() / 1000000.0);
    acutPrintf(L"Ceiling Height: %.2f m\n", pRoom->getCeilingHeight() / 1000.0);
    
    // Get new properties
    AcString roomName, roomNumber;
    double ceilingHeight;
    promptForRoomProperties(roomName, roomNumber, ceilingHeight);
    
    // Update properties
    pRoom->setRoomName(roomName.kACharPtr());
    pRoom->setRoomNumber(roomNumber.kACharPtr());
    pRoom->setCeilingHeight(ceilingHeight);
    
    pRoom->close();
    acutPrintf(L"\nRoom entity updated successfully.\n");
}

void RoomEntityCommands::addWindowCommand()
{
    acutPrintf(L"\nAdd Window to Room\n");
    
    AcDbObjectId entityId;
    if (!selectRoomEntity(entityId))
    {
        acutPrintf(L"\nNo room entity selected.\n");
        return;
    }
    
    AcGePoint3d startPt, endPt;
    if (!getWindowDoorPoints(startPt, endPt))
    {
        acutPrintf(L"\nCommand cancelled.\n");
        return;
    }
    
    RoomEntity* pRoom;
    if (!getRoomEntityForEdit(entityId, pRoom))
    {
        acutPrintf(L"\nError: Failed to open room entity for editing.\n");
        return;
    }
    
    WindowDoorSymbol window(SymbolType::Window, startPt, endPt);
    pRoom->addWindowDoor(window);
    
    pRoom->close();
    acutPrintf(L"\nWindow added successfully.\n");
}

void RoomEntityCommands::addDoorCommand()
{
    acutPrintf(L"\nAdd Door to Room\n");
    
    AcDbObjectId entityId;
    if (!selectRoomEntity(entityId))
    {
        acutPrintf(L"\nNo room entity selected.\n");
        return;
    }
    
    AcGePoint3d startPt, endPt;
    if (!getWindowDoorPoints(startPt, endPt))
    {
        acutPrintf(L"\nCommand cancelled.\n");
        return;
    }
    
    RoomEntity* pRoom;
    if (!getRoomEntityForEdit(entityId, pRoom))
    {
        acutPrintf(L"\nError: Failed to open room entity for editing.\n");
        return;
    }
    
    WindowDoorSymbol door(SymbolType::Door, startPt, endPt);
    pRoom->addWindowDoor(door);
    
    pRoom->close();
    acutPrintf(L"\nDoor added successfully.\n");
}

void RoomEntityCommands::toggleLabelsCommand()
{
    acutPrintf(L"\nToggle Room Labels\n");
    
    AcDbObjectId entityId;
    if (!selectRoomEntity(entityId))
    {
        acutPrintf(L"\nNo room entity selected.\n");
        return;
    }
    
    RoomEntity* pRoom;
    if (!getRoomEntityForEdit(entityId, pRoom))
    {
        acutPrintf(L"\nError: Failed to open room entity for editing.\n");
        return;
    }
    
    bool currentState = pRoom->getShowLabels();
    pRoom->setShowLabels(!currentState);
    
    pRoom->close();
    acutPrintf(L"\nLabels %s.\n", currentState ? L"hidden" : L"shown");
}

void RoomEntityCommands::calculateAreaCommand()
{
    acutPrintf(L"\nCalculate Room Area\n");
    
    AcDbObjectId entityId;
    if (!selectRoomEntity(entityId))
    {
        acutPrintf(L"\nNo room entity selected.\n");
        return;
    }
    
    const RoomEntity* pRoom;
    if (!getRoomEntityForRead(entityId, pRoom))
    {
        acutPrintf(L"\nError: Failed to open room entity for reading.\n");
        return;
    }
    
    displayRoomInfo(pRoom);
    pRoom->close();
}

void RoomEntityCommands::roomPropertiesCommand()
{
    acutPrintf(L"\nRoom Properties\n");
    
    AcDbObjectId entityId;
    if (!selectRoomEntity(entityId))
    {
        acutPrintf(L"\nNo room entity selected.\n");
        return;
    }
    
    const RoomEntity* pRoom;
    if (!getRoomEntityForRead(entityId, pRoom))
    {
        acutPrintf(L"\nError: Failed to open room entity for reading.\n");
        return;
    }
    
    displayRoomInfo(pRoom);
    pRoom->close();
}

bool RoomEntityCommands::selectRoomEntity(AcDbObjectId& entityId)
{
    ads_name ename;
    ads_point pt;
    
    int result = acedEntSel(L"\nSelect a room entity: ", ename, pt);
    if (result != RTNORM)
        return false;
    
    acdbGetObjectId(entityId, ename);
    
    // Verify it's a room entity
    AcDbEntity* pEnt;
    if (acdbOpenObject(pEnt, entityId, AcDb::kForRead) != Acad::eOk)
        return false;
    
    bool isRoomEntity = pEnt->isKindOf(RoomEntity::desc());
    pEnt->close();
    
    return isRoomEntity;
}

bool RoomEntityCommands::getRoomEntityForEdit(AcDbObjectId entityId, RoomEntity*& pRoom)
{
    return (acdbOpenObject(pRoom, entityId, AcDb::kForWrite) == Acad::eOk);
}

bool RoomEntityCommands::getRoomEntityForRead(AcDbObjectId entityId, const RoomEntity*& pRoom)
{
    RoomEntity* pNonConstRoom;
    if (acdbOpenObject(pNonConstRoom, entityId, AcDb::kForRead) == Acad::eOk)
    {
        pRoom = pNonConstRoom;
        return true;
    }
    return false;
}

void RoomEntityCommands::promptForRoomProperties(AcString& name, AcString& number, double& height)
{
    ACHAR buffer[256];
    
    // Get room name
    if (acedGetString(Adesk::kFalse, L"\nEnter room name <Room>: ", buffer) == RTNORM)
        name = buffer;
    else
        name = L"Room";
    
    // Get room number
    if (acedGetString(Adesk::kFalse, L"\nEnter room number <001>: ", buffer) == RTNORM)
        number = buffer;
    else
        number = L"001";
    
    // Get ceiling height
    double heightMeters = 2.7; // Default 2.7m
    if (acedGetReal(L"\nEnter ceiling height in meters <2.7>: ", &heightMeters) == RTNORM)
        height = heightMeters * 1000.0; // Convert to mm
    else
        height = 2700.0; // Default in mm
}

bool RoomEntityCommands::getPolygonPoints(AcGePoint3dArray& points)
{
    points.removeAll();
    
    acutPrintf(L"\nSpecify room boundary points (press Enter to finish):\n");
    
    AcGePoint3d pt;
    while (true)
    {
        AcString prompt;
        prompt.format(L"\nSpecify point %d: ", points.length() + 1);
        
        int result = acedGetPoint(nullptr, prompt.kACharPtr(), asDblArray(pt));
        if (result != RTNORM)
            break;
        
        points.append(pt);
        
        if (points.length() >= 3)
        {
            acutPrintf(L"\nPress Enter to finish or specify next point.");
        }
    }
    
    return (points.length() >= 3);
}

bool RoomEntityCommands::getWindowDoorPoints(AcGePoint3d& startPt, AcGePoint3d& endPt)
{
    if (acedGetPoint(nullptr, L"\nSpecify start point: ", asDblArray(startPt)) != RTNORM)
        return false;
    
    if (acedGetPoint(asDblArray(startPt), L"\nSpecify end point: ", asDblArray(endPt)) != RTNORM)
        return false;
    
    return true;
}

void RoomEntityCommands::displayRoomInfo(const RoomEntity* pRoom)
{
    acutPrintf(L"\n=== Room Information ===\n");
    acutPrintf(L"Name: %s\n", pRoom->getRoomName());
    acutPrintf(L"Number: %s\n", pRoom->getRoomNumber());
    acutPrintf(L"Area: %.2f m² (%.0f mm²)\n", 
               pRoom->getArea() / 1000000.0, pRoom->getArea());
    acutPrintf(L"Ceiling Height: %.2f m (%.0f mm)\n", 
               pRoom->getCeilingHeight() / 1000.0, pRoom->getCeilingHeight());
    acutPrintf(L"Vertices: %d\n", pRoom->getVertices().length());
    acutPrintf(L"Windows/Doors: %d\n", pRoom->getWindowDoorCount());
    acutPrintf(L"Labels Visible: %s\n", pRoom->getShowLabels() ? L"Yes" : L"No");
    acutPrintf(L"========================\n");
}
