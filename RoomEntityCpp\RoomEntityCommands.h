#pragma once
#include "pch.h"

class ROOMENTITY_API RoomEntityCommands
{
public:
    // Command registration
    static void registerCommands();
    static void unregisterCommands();

    // Command implementations
    static void createRoomCommand();
    static void editRoomCommand();
    static void addWindowCommand();
    static void addDoorCommand();
    static void toggleLabelsCommand();
    static void calculateAreaCommand();
    static void roomPropertiesCommand();

private:
    // Helper methods
    static bool selectRoomEntity(AcDbObjectId& entityId);
    static bool getRoomEntityForEdit(AcDbObjectId entityId, RoomEntity*& pRoom);
    static bool getRoomEntityForRead(AcDbObjectId entityId, const RoomEntity*& pRoom);
    static void promptForRoomProperties(AcString& name, AcString& number, double& height);
    static bool getPolygonPoints(AcGePoint3dArray& points);
    static bool getWindowDoorPoints(AcGePoint3d& startPt, AcGePoint3d& endPt);
    static void displayRoomInfo(const RoomEntity* pRoom);

    // Command group name
    static const ACHAR* COMMAND_GROUP;
};
