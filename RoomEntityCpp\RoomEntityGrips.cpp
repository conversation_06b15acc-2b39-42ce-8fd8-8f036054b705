#include "pch.h"
#include "RoomEntityGrips.h"
#include "RoomEntity.h"

// RoomEntityGrips implementation
RoomEntityGrips::RoomEntityGrips(RoomEntity* pEntity, int vertexIndex)
    : m_pRoomEntity(pEntity)
    , m_vertexIndex(vertexIndex)
    , m_isHot(false)
{
    if (pEntity && vertexIndex >= 0 && vertexIndex < pEntity->getVertices().length())
    {
        m_originalPoint = pEntity->getVertices()[vertexIndex];
        setGripPoint(m_originalPoint);
    }
}

RoomEntityGrips::~RoomEntityGrips()
{
}

Acad::ErrorStatus RoomEntityGrips::hotGrip(const AcGePoint3d& newPoint)
{
    if (!m_pRoomEntity)
        return Acad::eInvalidInput;

    // Update the vertex position
    AcGePoint3dArray vertices = m_pRoomEntity->getVertices();
    if (m_vertexIndex >= 0 && m_vertexIndex < vertices.length())
    {
        vertices[m_vertexIndex] = newPoint;
        m_pRoomEntity->setVertices(vertices);
        
        // Update grip point
        setGripPoint(newPoint);
        
        // Trigger regeneration
        m_pRoomEntity->recordGraphicsModified();
    }

    return Acad::eOk;
}

void RoomEntityGrips::viewportDraw(AcGiViewportDraw* pVd, AcDbGripData* pGrip)
{
    // Viewport-specific grip drawing if needed
}

bool RoomEntityGrips::worldDraw(AcGiWorldDraw* pWd, AcDbGripData* pGrip)
{
    if (!m_pRoomEntity)
        return false;

    // Draw the grip marker
    drawGripMarker(pWd);

    // Draw rubber band line if grip is hot
    if (m_isHot)
    {
        drawRubberBand(pWd, gripPoint());
    }

    return true;
}

AcDbGripData::GripType RoomEntityGrips::gripType() const
{
    return AcDbGripData::kDefaultGrip;
}

double RoomEntityGrips::gripSize() const
{
    return 5.0; // Default grip size
}

AcGePoint3d RoomEntityGrips::gripPoint() const
{
    if (m_pRoomEntity && m_vertexIndex >= 0 && m_vertexIndex < m_pRoomEntity->getVertices().length())
    {
        return m_pRoomEntity->getVertices()[m_vertexIndex];
    }
    return m_originalPoint;
}

bool RoomEntityGrips::isRubberBandLineDisabled() const
{
    return false; // Enable rubber band lines
}

void RoomEntityGrips::gripOpStatus(GripOpStatus status)
{
    switch (status)
    {
    case kGripStart:
        m_isHot = true;
        if (m_pRoomEntity)
            m_originalPoint = gripPoint();
        break;
    case kGripEnd:
        m_isHot = false;
        break;
    case kGripAbort:
        m_isHot = false;
        // Restore original position
        if (m_pRoomEntity)
        {
            AcGePoint3dArray vertices = m_pRoomEntity->getVertices();
            if (m_vertexIndex >= 0 && m_vertexIndex < vertices.length())
            {
                vertices[m_vertexIndex] = m_originalPoint;
                m_pRoomEntity->setVertices(vertices);
            }
        }
        break;
    }
}

void RoomEntityGrips::updateGripPoint()
{
    if (m_pRoomEntity && m_vertexIndex >= 0 && m_vertexIndex < m_pRoomEntity->getVertices().length())
    {
        setGripPoint(m_pRoomEntity->getVertices()[m_vertexIndex]);
    }
}

void RoomEntityGrips::drawGripMarker(AcGiWorldDraw* pWd) const
{
    // Set grip appearance
    pWd->subEntityTraits().setColor(m_isHot ? 1 : 2); // Red when hot, yellow when cold
    pWd->subEntityTraits().setLineWeight(AcDb::kLnWt025);

    // Draw grip as a small square
    AcGePoint3d center = gripPoint();
    double size = gripSize();
    
    AcGePoint3dArray gripPoints;
    gripPoints.append(AcGePoint3d(center.x - size, center.y - size, center.z));
    gripPoints.append(AcGePoint3d(center.x + size, center.y - size, center.z));
    gripPoints.append(AcGePoint3d(center.x + size, center.y + size, center.z));
    gripPoints.append(AcGePoint3d(center.x - size, center.y + size, center.z));
    gripPoints.append(AcGePoint3d(center.x - size, center.y - size, center.z)); // Close the square

    pWd->geometry().polyline(gripPoints.length(), gripPoints.asArrayPtr());
}

void RoomEntityGrips::drawRubberBand(AcGiWorldDraw* pWd, const AcGePoint3d& currentPoint) const
{
    if (!m_pRoomEntity)
        return;

    // Set rubber band appearance
    pWd->subEntityTraits().setColor(8); // Gray color
    pWd->subEntityTraits().setLineType(AcDb::kLtypeDashed);

    const AcGePoint3dArray& vertices = m_pRoomEntity->getVertices();
    int numVertices = vertices.length();

    if (numVertices < 2)
        return;

    // Draw lines to adjacent vertices
    int prevIndex = (m_vertexIndex - 1 + numVertices) % numVertices;
    int nextIndex = (m_vertexIndex + 1) % numVertices;

    // Line to previous vertex
    AcGePoint3dArray line1;
    line1.append(vertices[prevIndex]);
    line1.append(currentPoint);
    pWd->geometry().polyline(2, line1.asArrayPtr());

    // Line to next vertex
    AcGePoint3dArray line2;
    line2.append(currentPoint);
    line2.append(vertices[nextIndex]);
    pWd->geometry().polyline(2, line2.asArrayPtr());
}

// RoomEntityGripManager implementation
void RoomEntityGripManager::addGripsToEntity(RoomEntity* pEntity)
{
    if (!pEntity)
        return;

    AcDbGripDataPtrArray grips = createGripData(pEntity);
    pEntity->setGripData(grips);
}

void RoomEntityGripManager::removeGripsFromEntity(RoomEntity* pEntity)
{
    if (!pEntity)
        return;

    AcDbGripDataPtrArray emptyGrips;
    pEntity->setGripData(emptyGrips);
}

void RoomEntityGripManager::updateGrips(RoomEntity* pEntity)
{
    if (!pEntity)
        return;

    // Remove existing grips and create new ones
    removeGripsFromEntity(pEntity);
    addGripsToEntity(pEntity);
}

AcDbGripDataPtrArray RoomEntityGripManager::createGripData(RoomEntity* pEntity)
{
    AcDbGripDataPtrArray grips;

    if (!pEntity)
        return grips;

    const AcGePoint3dArray& vertices = pEntity->getVertices();
    
    // Create a grip for each vertex
    for (int i = 0; i < vertices.length(); i++)
    {
        RoomEntityGrips* pGrip = createVertexGrip(pEntity, i);
        if (pGrip)
        {
            grips.append(pGrip);
        }
    }

    return grips;
}

void RoomEntityGripManager::cleanupGripData(AcDbGripDataPtrArray& grips)
{
    for (int i = 0; i < grips.length(); i++)
    {
        delete grips[i];
    }
    grips.removeAll();
}

bool RoomEntityGripManager::isGripHot(const RoomEntityGrips* pGrip)
{
    return pGrip ? pGrip->m_isHot : false;
}

void RoomEntityGripManager::setGripHot(RoomEntityGrips* pGrip, bool hot)
{
    if (pGrip)
    {
        pGrip->m_isHot = hot;
    }
}

RoomEntityGrips* RoomEntityGripManager::createVertexGrip(RoomEntity* pEntity, int vertexIndex)
{
    if (!pEntity || vertexIndex < 0 || vertexIndex >= pEntity->getVertices().length())
        return nullptr;

    return new RoomEntityGrips(pEntity, vertexIndex);
}

void RoomEntityGripManager::updateEntityGeometry(RoomEntity* pEntity)
{
    if (!pEntity)
        return;

    // Recalculate area and validate geometry
    pEntity->calculateArea();
    pEntity->recordGraphicsModified();
}
