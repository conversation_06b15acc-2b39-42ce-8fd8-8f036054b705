#pragma once
#include "pch.h"

// Forward declaration
class RoomEntity;

class ROOMENTITY_API RoomEntityGrips : public AcDbGripData
{
public:
    // Constructor
    RoomEntityGrips(RoomEntity* pEntity, int vertexIndex);
    virtual ~RoomEntityGrips();

    // AcDbGripData overrides
    virtual Acad::ErrorStatus hotGrip(const AcGePoint3d& newPoint) override;
    virtual void viewportDraw(AcGiViewportDraw* pVd, AcDbGripData* pGrip) override;
    virtual bool worldDraw(AcGiWorldDraw* pWd, AcDbGripData* pGrip) override;

    // Grip-specific methods
    void setVertexIndex(int index) { m_vertexIndex = index; }
    int getVertexIndex() const { return m_vertexIndex; }

    void setRoomEntity(RoomEntity* pEntity) { m_pRoomEntity = pEntity; }
    RoomEntity* getRoomEntity() const { return m_pRoomEntity; }

    // Grip appearance
    virtual AcDbGripData::GripType gripType() const override;
    virtual double gripSize() const override;
    virtual AcGePoint3d gripPoint() const override;

    // Grip behavior
    virtual bool isRubberBandLineDisabled() const override;
    virtual void gripOpStatus(GripOpStatus status) override;

private:
    RoomEntity* m_pRoomEntity;
    int m_vertexIndex;
    AcGePoint3d m_originalPoint;
    bool m_isHot;

    // Helper methods
    void updateGripPoint();
    void drawGripMarker(AcGiWorldDraw* pWd) const;
    void drawRubberBand(AcGiWorldDraw* pWd, const AcGePoint3d& currentPoint) const;
};

// Grip manager class for room entities
class ROOMENTITY_API RoomEntityGripManager
{
public:
    // Static methods for grip management
    static void addGripsToEntity(RoomEntity* pEntity);
    static void removeGripsFromEntity(RoomEntity* pEntity);
    static void updateGrips(RoomEntity* pEntity);

    // Grip creation and management
    static AcDbGripDataPtrArray createGripData(RoomEntity* pEntity);
    static void cleanupGripData(AcDbGripDataPtrArray& grips);

    // Grip interaction
    static bool isGripHot(const RoomEntityGrips* pGrip);
    static void setGripHot(RoomEntityGrips* pGrip, bool hot);

private:
    // Internal helper methods
    static RoomEntityGrips* createVertexGrip(RoomEntity* pEntity, int vertexIndex);
    static void updateEntityGeometry(RoomEntity* pEntity);
};
