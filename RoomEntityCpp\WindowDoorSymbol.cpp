#include "pch.h"
#include "WindowDoorSymbol.h"

// Static member definitions
const double WindowDoorSymbol::DEFAULT_DOOR_SWING_ANGLE = 90.0; // degrees
const double WindowDoorSymbol::WINDOW_SILL_OFFSET = 50.0; // mm
const double WindowDoorSymbol::MIN_SYMBOL_WIDTH = 100.0; // mm
const double WindowDoorSymbol::MAX_SYMBOL_WIDTH = 3000.0; // mm

WindowDoorSymbol::WindowDoorSymbol()
    : m_type(SymbolType::Window)
    , m_startPoint(AcGePoint3d::kOrigin)
    , m_endPoint(AcGePoint3d(1000.0, 0.0, 0.0))
    , m_name(L"Symbol")
{
}

WindowDoorSymbol::WindowDoorSymbol(SymbolType type, const AcGePoint3d& startPoint, const AcGePoint3d& endPoint)
    : m_type(type)
    , m_startPoint(startPoint)
    , m_endPoint(endPoint)
    , m_name(getTypeName(type))
{
    validate();
}

WindowDoorSymbol::WindowDoorSymbol(const WindowDoorSymbol& other)
    : m_type(other.m_type)
    , m_startPoint(other.m_startPoint)
    , m_endPoint(other.m_endPoint)
    , m_name(other.m_name)
{
}

WindowDoorSymbol::~WindowDoorSymbol()
{
}

WindowDoorSymbol& WindowDoorSymbol::operator=(const WindowDoorSymbol& other)
{
    if (this != &other)
    {
        m_type = other.m_type;
        m_startPoint = other.m_startPoint;
        m_endPoint = other.m_endPoint;
        m_name = other.m_name;
    }
    return *this;
}

double WindowDoorSymbol::getWidth() const
{
    return m_startPoint.distanceTo(m_endPoint);
}

AcGeVector3d WindowDoorSymbol::getDirection() const
{
    AcGeVector3d dir = m_endPoint - m_startPoint;
    dir.normalize();
    return dir;
}

AcGePoint3d WindowDoorSymbol::getMidPoint() const
{
    return m_startPoint + (m_endPoint - m_startPoint) * 0.5;
}

void WindowDoorSymbol::draw(AcGiWorldDraw* pWd) const
{
    if (!isValid())
        return;

    switch (m_type)
    {
    case SymbolType::Window:
        drawWindow(pWd);
        break;
    case SymbolType::Door:
        drawDoor(pWd);
        break;
    case SymbolType::SlidingDoor:
        drawSlidingDoor(pWd);
        break;
    case SymbolType::DoubleDoor:
        drawDoubleDoor(pWd);
        break;
    }
}

void WindowDoorSymbol::drawWindow(AcGiWorldDraw* pWd) const
{
    // Set window color (blue)
    pWd->subEntityTraits().setColor(5);
    pWd->subEntityTraits().setLineWeight(AcDb::kLnWt015);

    // Draw main window line
    drawLine(pWd, m_startPoint, m_endPoint);

    // Draw window sill lines (parallel offset lines)
    AcGeVector3d dir = getDirection();
    AcGeVector3d perpDir = dir.perpVector() * WINDOW_SILL_OFFSET;

    AcGePoint3d sillStart1 = m_startPoint + perpDir;
    AcGePoint3d sillEnd1 = m_endPoint + perpDir;
    AcGePoint3d sillStart2 = m_startPoint - perpDir;
    AcGePoint3d sillEnd2 = m_endPoint - perpDir;

    drawLine(pWd, sillStart1, sillEnd1);
    drawLine(pWd, sillStart2, sillEnd2);

    // Draw window frame (cross pattern)
    AcGePoint3d midPoint = getMidPoint();
    drawLine(pWd, midPoint + perpDir, midPoint - perpDir);
}

void WindowDoorSymbol::drawDoor(AcGiWorldDraw* pWd) const
{
    // Set door color (green)
    pWd->subEntityTraits().setColor(3);
    pWd->subEntityTraits().setLineWeight(AcDb::kLnWt025);

    // Draw door opening line
    drawLine(pWd, m_startPoint, m_endPoint);

    // Draw door swing arc
    double width = getWidth();
    AcGeVector3d dir = getDirection();
    AcGeVector3d perpDir = dir.perpVector();

    // Door swings from start point
    AcGePoint3d hingePoint = m_startPoint;
    AcGePoint3d swingEnd = hingePoint + perpDir * width;

    // Draw swing arc (quarter circle)
    double startAngle = dir.angle();
    double endAngle = startAngle + (DEFAULT_DOOR_SWING_ANGLE * M_PI / 180.0);
    
    drawArc(pWd, hingePoint, width, startAngle, endAngle);

    // Draw door panel line
    drawLine(pWd, hingePoint, swingEnd);
}

void WindowDoorSymbol::drawSlidingDoor(AcGiWorldDraw* pWd) const
{
    // Set sliding door color (cyan)
    pWd->subEntityTraits().setColor(4);
    pWd->subEntityTraits().setLineWeight(AcDb::kLnWt020);

    // Draw main opening line
    drawLine(pWd, m_startPoint, m_endPoint);

    // Draw sliding track
    AcGeVector3d dir = getDirection();
    AcGeVector3d perpDir = dir.perpVector() * 25.0; // Track offset

    AcGePoint3d trackStart = m_startPoint + perpDir;
    AcGePoint3d trackEnd = m_endPoint + perpDir;
    drawLine(pWd, trackStart, trackEnd);

    // Draw sliding panels (two panels)
    double halfWidth = getWidth() * 0.5;
    AcGePoint3d panel1Start = m_startPoint;
    AcGePoint3d panel1End = m_startPoint + dir * halfWidth;
    AcGePoint3d panel2Start = panel1End;
    AcGePoint3d panel2End = m_endPoint;

    // Offset panels slightly
    AcGeVector3d panelOffset = perpDir * 0.5;
    drawLine(pWd, panel1Start + panelOffset, panel1End + panelOffset);
    drawLine(pWd, panel2Start - panelOffset, panel2End - panelOffset);
}

void WindowDoorSymbol::drawDoubleDoor(AcGiWorldDraw* pWd) const
{
    // Set double door color (magenta)
    pWd->subEntityTraits().setColor(6);
    pWd->subEntityTraits().setLineWeight(AcDb::kLnWt025);

    // Draw door opening line
    drawLine(pWd, m_startPoint, m_endPoint);

    // Draw two door swings
    double halfWidth = getWidth() * 0.5;
    AcGeVector3d dir = getDirection();
    AcGeVector3d perpDir = dir.perpVector();

    // Left door (swings left)
    AcGePoint3d leftHinge = m_startPoint;
    AcGePoint3d leftSwingEnd = leftHinge - perpDir * halfWidth;
    drawArc(pWd, leftHinge, halfWidth, dir.angle() - M_PI_2, dir.angle());
    drawLine(pWd, leftHinge, leftSwingEnd);

    // Right door (swings right)
    AcGePoint3d rightHinge = m_endPoint;
    AcGePoint3d rightSwingEnd = rightHinge + perpDir * halfWidth;
    drawArc(pWd, rightHinge, halfWidth, dir.angle(), dir.angle() + M_PI_2);
    drawLine(pWd, rightHinge, rightSwingEnd);
}

void WindowDoorSymbol::drawLine(AcGiWorldDraw* pWd, const AcGePoint3d& start, const AcGePoint3d& end) const
{
    AcGePoint3dArray points;
    points.append(start);
    points.append(end);
    pWd->geometry().polyline(2, points.asArrayPtr());
}

void WindowDoorSymbol::drawArc(AcGiWorldDraw* pWd, const AcGePoint3d& center, double radius, 
                               double startAngle, double endAngle) const
{
    AcGeCircArc3d arc(center, AcGeVector3d::kZAxis, radius);
    arc.setAngles(startAngle, endAngle);
    pWd->geometry().circularArc(center, radius, AcGeVector3d::kZAxis, AcGeVector3d::kXAxis, 
                               radius, startAngle, endAngle);
}

void WindowDoorSymbol::drawPolyline(AcGiWorldDraw* pWd, const AcGePoint3dArray& points) const
{
    if (points.length() >= 2)
    {
        pWd->geometry().polyline(points.length(), points.asArrayPtr());
    }
}

bool WindowDoorSymbol::isPointNear(const AcGePoint3d& point, double tolerance) const
{
    AcGeLine3d line(m_startPoint, m_endPoint);
    AcGePoint3d closestPoint = line.closestPointTo(point);
    
    // Check if point is within tolerance and within the segment bounds
    if (point.distanceTo(closestPoint) <= tolerance)
    {
        double param = line.paramOf(closestPoint);
        return (param >= 0.0 && param <= 1.0);
    }
    
    return false;
}

void WindowDoorSymbol::transformBy(const AcGeMatrix3d& xform)
{
    m_startPoint.transformBy(xform);
    m_endPoint.transformBy(xform);
}

void WindowDoorSymbol::getExtents(AcDbExtents& extents) const
{
    extents.set(m_startPoint, m_startPoint);
    extents.addPoint(m_endPoint);
    
    // Add some padding for the symbol graphics
    AcGeVector3d padding(100.0, 100.0, 0.0);
    extents.addPoint(m_startPoint - padding);
    extents.addPoint(m_endPoint + padding);
}

bool WindowDoorSymbol::isValid() const
{
    double width = getWidth();
    return (width >= MIN_SYMBOL_WIDTH && width <= MAX_SYMBOL_WIDTH);
}

void WindowDoorSymbol::validate()
{
    double width = getWidth();
    if (width < MIN_SYMBOL_WIDTH)
    {
        // Extend to minimum width
        AcGeVector3d dir = getDirection();
        m_endPoint = m_startPoint + dir * MIN_SYMBOL_WIDTH;
    }
    else if (width > MAX_SYMBOL_WIDTH)
    {
        // Reduce to maximum width
        AcGeVector3d dir = getDirection();
        m_endPoint = m_startPoint + dir * MAX_SYMBOL_WIDTH;
    }
}

const ACHAR* WindowDoorSymbol::getTypeName(SymbolType type)
{
    switch (type)
    {
    case SymbolType::Window: return L"Window";
    case SymbolType::Door: return L"Door";
    case SymbolType::SlidingDoor: return L"Sliding Door";
    case SymbolType::DoubleDoor: return L"Double Door";
    default: return L"Unknown";
    }
}

SymbolType WindowDoorSymbol::getTypeFromName(const ACHAR* name)
{
    if (!name) return SymbolType::Window;
    
    AcString nameStr(name);
    nameStr.makeLower();
    
    if (nameStr.find(L"door") != -1)
    {
        if (nameStr.find(L"sliding") != -1)
            return SymbolType::SlidingDoor;
        else if (nameStr.find(L"double") != -1)
            return SymbolType::DoubleDoor;
        else
            return SymbolType::Door;
    }
    
    return SymbolType::Window;
}

Acad::ErrorStatus WindowDoorSymbol::dwgInFields(AcDbDwgFiler* pFiler)
{
    // Read symbol type
    Adesk::UInt16 type;
    pFiler->readUInt16(&type);
    m_type = static_cast<SymbolType>(type);

    // Read points
    pFiler->readPoint3d(&m_startPoint);
    pFiler->readPoint3d(&m_endPoint);

    // Read name
    pFiler->readString(m_name);

    validate();
    return Acad::eOk;
}

Acad::ErrorStatus WindowDoorSymbol::dwgOutFields(AcDbDwgFiler* pFiler) const
{
    // Write symbol type
    pFiler->writeUInt16(static_cast<Adesk::UInt16>(m_type));

    // Write points
    pFiler->writePoint3d(m_startPoint);
    pFiler->writePoint3d(m_endPoint);

    // Write name
    pFiler->writeString(m_name);

    return Acad::eOk;
}

Acad::ErrorStatus WindowDoorSymbol::dxfInFields(AcDbDxfFiler* pFiler)
{
    // Simplified DXF implementation
    // In a full implementation, this would handle specific DXF group codes
    return Acad::eOk;
}

Acad::ErrorStatus WindowDoorSymbol::dxfOutFields(AcDbDxfFiler* pFiler) const
{
    // Simplified DXF implementation
    // In a full implementation, this would write specific DXF group codes
    return Acad::eOk;
}
