#pragma once
#include "pch.h"

enum class SymbolType
{
    Window,
    Door,
    SlidingDoor,
    DoubleDoor
};

class ROOMENTITY_API WindowDoorSymbol
{
public:
    // Constructors
    WindowDoorSymbol();
    WindowDoorSymbol(SymbolType type, const AcGePoint3d& startPoint, const AcGePoint3d& endPoint);
    WindowDoorSymbol(const WindowDoorSymbol& other);
    virtual ~WindowDoorSymbol();

    // Assignment operator
    WindowDoorSymbol& operator=(const WindowDoorSymbol& other);

    // Property access
    SymbolType getType() const { return m_type; }
    void setType(SymbolType type) { m_type = type; }

    const AcGePoint3d& getStartPoint() const { return m_startPoint; }
    void setStartPoint(const AcGePoint3d& point) { m_startPoint = point; }

    const AcGePoint3d& getEndPoint() const { return m_endPoint; }
    void setEndPoint(const AcGePoint3d& point) { m_endPoint = point; }

    double getWidth() const;
    AcGeVector3d getDirection() const;
    AcGePoint3d getMidPoint() const;

    const ACHAR* getName() const { return m_name.kACharPtr(); }
    void setName(const ACHAR* name) { m_name = name; }

    // Drawing methods
    void draw(AcGiWorldDraw* pWd) const;
    void drawWindow(AcGiWorldDraw* pWd) const;
    void drawDoor(AcGiWorldDraw* pWd) const;
    void drawSlidingDoor(AcGiWorldDraw* pWd) const;
    void drawDoubleDoor(AcGiWorldDraw* pWd) const;

    // Geometric queries
    bool isPointNear(const AcGePoint3d& point, double tolerance = 1.0) const;
    void transformBy(const AcGeMatrix3d& xform);
    void getExtents(AcDbExtents& extents) const;

    // Serialization
    Acad::ErrorStatus dwgInFields(AcDbDwgFiler* pFiler);
    Acad::ErrorStatus dwgOutFields(AcDbDwgFiler* pFiler) const;
    Acad::ErrorStatus dxfInFields(AcDbDxfFiler* pFiler);
    Acad::ErrorStatus dxfOutFields(AcDbDxfFiler* pFiler) const;

    // Validation
    bool isValid() const;
    void validate();

    // Static utility methods
    static const ACHAR* getTypeName(SymbolType type);
    static SymbolType getTypeFromName(const ACHAR* name);

private:
    SymbolType m_type;
    AcGePoint3d m_startPoint;
    AcGePoint3d m_endPoint;
    AcString m_name;

    // Drawing helper methods
    void drawLine(AcGiWorldDraw* pWd, const AcGePoint3d& start, const AcGePoint3d& end) const;
    void drawArc(AcGiWorldDraw* pWd, const AcGePoint3d& center, double radius, 
                 double startAngle, double endAngle) const;
    void drawPolyline(AcGiWorldDraw* pWd, const AcGePoint3dArray& points) const;

    // Constants
    static const double DEFAULT_DOOR_SWING_ANGLE;
    static const double WINDOW_SILL_OFFSET;
    static const double MIN_SYMBOL_WIDTH;
    static const double MAX_SYMBOL_WIDTH;
};
