#include "pch.h"
#include "RoomEntity.h"
#include "RoomEntityCommands.h"

// Global variables
HINSTANCE g_hInstance = nullptr;

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        g_hInstance = hModule;
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

// ObjectARX entry point
extern "C" AcRx::AppRetCode acrxEntryPoint(AcRx::AppMsgCode msg, void* pkt)
{
    switch (msg)
    {
    case AcRx::kInitAppMsg:
        // Initialize the application
        acrxDynamicLinker->unlockApplication(pkt);
        acrxDynamicLinker->registerAppMDIAware(pkt);
        
        // Register the custom entity
        RoomEntity::rxInit();
        acrxBuildClassHierarchy();
        
        // Register commands
        RoomEntityCommands::registerCommands();
        
        acutPrintf(L"\nRoom Entity application loaded successfully.\n");
        break;

    case AcRx::kUnloadAppMsg:
        // Unregister commands
        RoomEntityCommands::unregisterCommands();
        
        // Unregister the custom entity
        RoomEntity::rxUninit();
        
        acutPrintf(L"\nRoom Entity application unloaded.\n");
        break;

    case AcRx::kLoadDwgMsg:
        // Drawing loaded
        break;

    case AcRx::kUnloadDwgMsg:
        // Drawing unloaded
        break;

    case AcRx::kInvkSubrMsg:
        // Subroutine invoked
        break;

    default:
        break;
    }

    return AcRx::kRetOK;
}

// Get API version
extern "C" int acrxGetApiVersion()
{
    return ACAD_API_VERSION;
}
