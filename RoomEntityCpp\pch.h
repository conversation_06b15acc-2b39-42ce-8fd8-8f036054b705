#pragma once

// Windows headers
#include <windows.h>

// Standard C++ headers
#include <vector>
#include <string>
#include <memory>
#include <algorithm>
#include <map>

// ObjectARX headers
#include "arxHeaders.h"
#include "acdb.h"
#include "dbents.h"
#include "dbmain.h"
#include "dbsymtb.h"
#include "aced.h"
#include "acedads.h"
#include "acgi.h"
#include "acgs.h"
#include "AcGiWorldDraw.h"
#include "AcGiViewportDraw.h"
#include "acutads.h"
#include "adscodes.h"
#include "rxregsvc.h"
#include "rxdlinkr.h"
#include "rxmfcapi.h"
#include "acaplmgr.h"
#include "acedCmdNF.h"
#include "dbgrip.h"
#include "dbxutil.h"

// Common definitions
#define ROOM_ENTITY_CLASS_NAME L"RoomEntity"
#define ROOM_ENTITY_DXF_NAME L"ROOMENTITY"
#define ROOM_ENTITY_APP_NAME L"ROOMENTITY_APP"

// Export macros
#ifdef ROOMENTITYCPP_EXPORTS
#define ROOMENTITY_API __declspec(dllexport)
#else
#define ROOMENTITY_API __declspec(dllimport)
#endif
