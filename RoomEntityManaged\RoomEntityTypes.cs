using System;
using Autodesk.AutoCAD.Geometry;

namespace RoomEntityManaged
{
    /// <summary>
    /// Enumeration for window and door types
    /// </summary>
    public enum WindowDoorType
    {
        /// <summary>
        /// Standard window
        /// </summary>
        Window = 0,

        /// <summary>
        /// Standard door
        /// </summary>
        Door = 1,

        /// <summary>
        /// Sliding door
        /// </summary>
        SlidingDoor = 2,

        /// <summary>
        /// Double door
        /// </summary>
        DoubleDoor = 3
    }

    /// <summary>
    /// Information about a window or door symbol
    /// </summary>
    public class WindowDoorInfo
    {
        /// <summary>
        /// Type of the symbol
        /// </summary>
        public WindowDoorType Type { get; set; }

        /// <summary>
        /// Start point of the symbol
        /// </summary>
        public Point3d StartPoint { get; set; }

        /// <summary>
        /// End point of the symbol
        /// </summary>
        public Point3d EndPoint { get; set; }

        /// <summary>
        /// Name of the symbol
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Width of the symbol
        /// </summary>
        public double Width => StartPoint.DistanceTo(EndPoint);

        /// <summary>
        /// Midpoint of the symbol
        /// </summary>
        public Point3d MidPoint => StartPoint + (EndPoint - StartPoint) * 0.5;

        /// <summary>
        /// Direction vector of the symbol
        /// </summary>
        public Vector3d Direction
        {
            get
            {
                var dir = EndPoint - StartPoint;
                return dir.GetNormal();
            }
        }

        /// <summary>
        /// Default constructor
        /// </summary>
        public WindowDoorInfo()
        {
        }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        /// <param name="type">Symbol type</param>
        /// <param name="startPoint">Start point</param>
        /// <param name="endPoint">End point</param>
        /// <param name="name">Symbol name</param>
        public WindowDoorInfo(WindowDoorType type, Point3d startPoint, Point3d endPoint, string name = "")
        {
            Type = type;
            StartPoint = startPoint;
            EndPoint = endPoint;
            Name = name;
        }

        /// <summary>
        /// Returns a string representation of the symbol
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{Type}: {Name} (Width: {Width:F2}mm)";
        }
    }

    /// <summary>
    /// Comprehensive information about a room
    /// </summary>
    public class RoomInfo
    {
        /// <summary>
        /// Room name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Room number
        /// </summary>
        public string Number { get; set; } = string.Empty;

        /// <summary>
        /// Area in square millimeters
        /// </summary>
        public double Area { get; set; }

        /// <summary>
        /// Area in square meters
        /// </summary>
        public double AreaInSquareMeters { get; set; }

        /// <summary>
        /// Ceiling height in millimeters
        /// </summary>
        public double CeilingHeight { get; set; }

        /// <summary>
        /// Ceiling height in meters
        /// </summary>
        public double CeilingHeightInMeters { get; set; }

        /// <summary>
        /// Number of vertices
        /// </summary>
        public int VertexCount { get; set; }

        /// <summary>
        /// Number of windows and doors
        /// </summary>
        public int WindowDoorCount { get; set; }

        /// <summary>
        /// Whether labels are visible
        /// </summary>
        public bool ShowLabels { get; set; }

        /// <summary>
        /// Centroid point of the room
        /// </summary>
        public Point3d Centroid { get; set; }

        /// <summary>
        /// Volume in cubic millimeters
        /// </summary>
        public double Volume => Area * CeilingHeight;

        /// <summary>
        /// Volume in cubic meters
        /// </summary>
        public double VolumeInCubicMeters => Volume / 1000000000.0;

        /// <summary>
        /// Default constructor
        /// </summary>
        public RoomInfo()
        {
        }

        /// <summary>
        /// Returns a formatted string representation of the room information
        /// </summary>
        /// <returns>Formatted room information</returns>
        public override string ToString()
        {
            return $"Room {Number}: {Name}\n" +
                   $"Area: {AreaInSquareMeters:F2} m² ({Area:F0} mm²)\n" +
                   $"Height: {CeilingHeightInMeters:F2} m ({CeilingHeight:F0} mm)\n" +
                   $"Volume: {VolumeInCubicMeters:F2} m³\n" +
                   $"Vertices: {VertexCount}, Windows/Doors: {WindowDoorCount}";
        }

        /// <summary>
        /// Returns a detailed string representation
        /// </summary>
        /// <returns>Detailed room information</returns>
        public string ToDetailedString()
        {
            return $"=== Room Information ===\n" +
                   $"Name: {Name}\n" +
                   $"Number: {Number}\n" +
                   $"Area: {AreaInSquareMeters:F2} m² ({Area:F0} mm²)\n" +
                   $"Ceiling Height: {CeilingHeightInMeters:F2} m ({CeilingHeight:F0} mm)\n" +
                   $"Volume: {VolumeInCubicMeters:F2} m³ ({Volume:F0} mm³)\n" +
                   $"Vertices: {VertexCount}\n" +
                   $"Windows/Doors: {WindowDoorCount}\n" +
                   $"Labels Visible: {(ShowLabels ? "Yes" : "No")}\n" +
                   $"Centroid: ({Centroid.X:F2}, {Centroid.Y:F2}, {Centroid.Z:F2})\n" +
                   $"========================";
        }
    }

    /// <summary>
    /// Event arguments for room property changes
    /// </summary>
    public class RoomPropertyChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Name of the property that changed
        /// </summary>
        public string PropertyName { get; }

        /// <summary>
        /// Old value of the property
        /// </summary>
        public object? OldValue { get; }

        /// <summary>
        /// New value of the property
        /// </summary>
        public object? NewValue { get; }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="propertyName">Name of the changed property</param>
        /// <param name="oldValue">Old value</param>
        /// <param name="newValue">New value</param>
        public RoomPropertyChangedEventArgs(string propertyName, object? oldValue, object? newValue)
        {
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }

    /// <summary>
    /// Validation result for room data
    /// </summary>
    public class RoomValidationResult
    {
        /// <summary>
        /// Whether the room data is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Adds an error message
        /// </summary>
        /// <param name="error">Error message</param>
        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        /// <summary>
        /// Adds a warning message
        /// </summary>
        /// <param name="warning">Warning message</param>
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        /// <summary>
        /// Returns a summary of validation results
        /// </summary>
        /// <returns>Validation summary</returns>
        public override string ToString()
        {
            var result = $"Validation Result: {(IsValid ? "Valid" : "Invalid")}\n";
            
            if (Errors.Count > 0)
            {
                result += $"Errors ({Errors.Count}):\n";
                foreach (var error in Errors)
                    result += $"  - {error}\n";
            }
            
            if (Warnings.Count > 0)
            {
                result += $"Warnings ({Warnings.Count}):\n";
                foreach (var warning in Warnings)
                    result += $"  - {warning}\n";
            }
            
            return result;
        }
    }

    /// <summary>
    /// Constants for room entity
    /// </summary>
    public static class RoomConstants
    {
        /// <summary>
        /// Minimum ceiling height in millimeters
        /// </summary>
        public const double MinCeilingHeight = 1000.0; // 1 meter

        /// <summary>
        /// Default ceiling height in millimeters
        /// </summary>
        public const double DefaultCeilingHeight = 2700.0; // 2.7 meters

        /// <summary>
        /// Maximum ceiling height in millimeters
        /// </summary>
        public const double MaxCeilingHeight = 10000.0; // 10 meters

        /// <summary>
        /// Minimum number of vertices for a room
        /// </summary>
        public const int MinVertices = 3;

        /// <summary>
        /// Maximum number of vertices for a room
        /// </summary>
        public const int MaxVertices = 100;

        /// <summary>
        /// Minimum area in square millimeters
        /// </summary>
        public const double MinArea = 1000000.0; // 1 square meter

        /// <summary>
        /// Maximum area in square millimeters
        /// </summary>
        public const double MaxArea = 1000000000000.0; // 1 million square meters

        /// <summary>
        /// Tolerance for geometric calculations
        /// </summary>
        public const double GeometricTolerance = 0.001; // 1mm

        /// <summary>
        /// Default room name
        /// </summary>
        public const string DefaultRoomName = "Room";

        /// <summary>
        /// Default room number
        /// </summary>
        public const string DefaultRoomNumber = "001";
    }
}
