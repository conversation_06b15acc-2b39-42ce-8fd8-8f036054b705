using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace RoomEntityManaged
{
    /// <summary>
    /// Managed wrapper for the native RoomEntity C++ class
    /// </summary>
    [Wrapper("RoomEntity")]
    public class RoomEntityWrapper : Entity
    {
        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public RoomEntityWrapper() : base()
        {
        }

        /// <summary>
        /// Constructor with initial properties
        /// </summary>
        /// <param name="roomName">Name of the room</param>
        /// <param name="roomNumber">Room number</param>
        /// <param name="ceilingHeight">Ceiling height in millimeters</param>
        public RoomEntityWrapper(string roomName, string roomNumber, double ceilingHeight) : base()
        {
            RoomName = roomName;
            RoomNumber = roomNumber;
            CeilingHeight = ceilingHeight;
        }

        /// <summary>
        /// Constructor with vertices
        /// </summary>
        /// <param name="vertices">Room boundary vertices</param>
        public RoomEntityWrapper(Point3dCollection vertices) : base()
        {
            SetVertices(vertices);
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets the room name
        /// </summary>
        public string RoomName
        {
            get
            {
                CheckObjectState();
                return GetRoomName();
            }
            set
            {
                CheckObjectState();
                SetRoomName(value ?? string.Empty);
            }
        }

        /// <summary>
        /// Gets or sets the room number
        /// </summary>
        public string RoomNumber
        {
            get
            {
                CheckObjectState();
                return GetRoomNumber();
            }
            set
            {
                CheckObjectState();
                SetRoomNumber(value ?? string.Empty);
            }
        }

        /// <summary>
        /// Gets the calculated area in square millimeters
        /// </summary>
        public double Area
        {
            get
            {
                CheckObjectState();
                return GetArea();
            }
        }

        /// <summary>
        /// Gets the area in square meters
        /// </summary>
        public double AreaInSquareMeters => Area / 1000000.0;

        /// <summary>
        /// Gets or sets the ceiling height in millimeters
        /// </summary>
        public double CeilingHeight
        {
            get
            {
                CheckObjectState();
                return GetCeilingHeight();
            }
            set
            {
                CheckObjectState();
                SetCeilingHeight(value);
            }
        }

        /// <summary>
        /// Gets the ceiling height in meters
        /// </summary>
        public double CeilingHeightInMeters
        {
            get => CeilingHeight / 1000.0;
            set => CeilingHeight = value * 1000.0;
        }

        /// <summary>
        /// Gets or sets whether property labels are visible
        /// </summary>
        public bool ShowLabels
        {
            get
            {
                CheckObjectState();
                return GetShowLabels();
            }
            set
            {
                CheckObjectState();
                SetShowLabels(value);
            }
        }

        /// <summary>
        /// Gets the room vertices
        /// </summary>
        public Point3dCollection Vertices
        {
            get
            {
                CheckObjectState();
                return GetVertices();
            }
        }

        /// <summary>
        /// Gets the number of vertices
        /// </summary>
        public int VertexCount => Vertices?.Count ?? 0;

        /// <summary>
        /// Gets the number of windows and doors
        /// </summary>
        public int WindowDoorCount
        {
            get
            {
                CheckObjectState();
                return GetWindowDoorCount();
            }
        }

        /// <summary>
        /// Gets the centroid point of the room
        /// </summary>
        public Point3d Centroid
        {
            get
            {
                CheckObjectState();
                return GetCentroid();
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Sets the room vertices
        /// </summary>
        /// <param name="vertices">Collection of vertices</param>
        public void SetVertices(Point3dCollection vertices)
        {
            CheckObjectState();
            if (vertices == null)
                throw new ArgumentNullException(nameof(vertices));
            
            if (vertices.Count < 3)
                throw new ArgumentException("Room must have at least 3 vertices", nameof(vertices));

            SetVerticesInternal(vertices);
            RecalculateArea();
        }

        /// <summary>
        /// Adds a vertex to the room
        /// </summary>
        /// <param name="vertex">Vertex to add</param>
        public void AddVertex(Point3d vertex)
        {
            CheckObjectState();
            var vertices = GetVertices();
            vertices.Add(vertex);
            SetVerticesInternal(vertices);
            RecalculateArea();
        }

        /// <summary>
        /// Removes a vertex at the specified index
        /// </summary>
        /// <param name="index">Index of vertex to remove</param>
        public void RemoveVertex(int index)
        {
            CheckObjectState();
            var vertices = GetVertices();
            
            if (index < 0 || index >= vertices.Count)
                throw new ArgumentOutOfRangeException(nameof(index));
            
            if (vertices.Count <= 3)
                throw new InvalidOperationException("Cannot remove vertex - room must have at least 3 vertices");

            vertices.RemoveAt(index);
            SetVerticesInternal(vertices);
            RecalculateArea();
        }

        /// <summary>
        /// Updates a vertex at the specified index
        /// </summary>
        /// <param name="index">Index of vertex to update</param>
        /// <param name="newVertex">New vertex position</param>
        public void UpdateVertex(int index, Point3d newVertex)
        {
            CheckObjectState();
            var vertices = GetVertices();
            
            if (index < 0 || index >= vertices.Count)
                throw new ArgumentOutOfRangeException(nameof(index));

            vertices[index] = newVertex;
            SetVerticesInternal(vertices);
            RecalculateArea();
        }

        /// <summary>
        /// Adds a window to the room
        /// </summary>
        /// <param name="startPoint">Start point of the window</param>
        /// <param name="endPoint">End point of the window</param>
        /// <param name="name">Optional name for the window</param>
        public void AddWindow(Point3d startPoint, Point3d endPoint, string name = "Window")
        {
            CheckObjectState();
            AddWindowDoorSymbol(WindowDoorType.Window, startPoint, endPoint, name);
        }

        /// <summary>
        /// Adds a door to the room
        /// </summary>
        /// <param name="startPoint">Start point of the door</param>
        /// <param name="endPoint">End point of the door</param>
        /// <param name="name">Optional name for the door</param>
        public void AddDoor(Point3d startPoint, Point3d endPoint, string name = "Door")
        {
            CheckObjectState();
            AddWindowDoorSymbol(WindowDoorType.Door, startPoint, endPoint, name);
        }

        /// <summary>
        /// Removes a window or door at the specified index
        /// </summary>
        /// <param name="index">Index of the symbol to remove</param>
        public void RemoveWindowDoor(int index)
        {
            CheckObjectState();
            RemoveWindowDoorSymbol(index);
        }

        /// <summary>
        /// Gets information about a window or door
        /// </summary>
        /// <param name="index">Index of the symbol</param>
        /// <returns>Window/door information</returns>
        public WindowDoorInfo GetWindowDoorInfo(int index)
        {
            CheckObjectState();
            return GetWindowDoorSymbol(index);
        }

        /// <summary>
        /// Checks if a point is inside the room
        /// </summary>
        /// <param name="point">Point to test</param>
        /// <returns>True if point is inside the room</returns>
        public bool IsPointInside(Point3d point)
        {
            CheckObjectState();
            return IsPointInsideRoom(point);
        }

        /// <summary>
        /// Recalculates the room area
        /// </summary>
        public void RecalculateArea()
        {
            CheckObjectState();
            CalculateArea();
        }

        /// <summary>
        /// Gets a summary of room information
        /// </summary>
        /// <returns>Room information summary</returns>
        public RoomInfo GetRoomInfo()
        {
            CheckObjectState();
            return new RoomInfo
            {
                Name = RoomName,
                Number = RoomNumber,
                Area = Area,
                AreaInSquareMeters = AreaInSquareMeters,
                CeilingHeight = CeilingHeight,
                CeilingHeightInMeters = CeilingHeightInMeters,
                VertexCount = VertexCount,
                WindowDoorCount = WindowDoorCount,
                ShowLabels = ShowLabels,
                Centroid = Centroid
            };
        }

        #endregion

        #region Native Method Declarations

        // These methods would be implemented as P/Invoke calls to the native C++ DLL
        // or through ObjectARX managed wrappers

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern string GetRoomName();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void SetRoomName(string name);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern string GetRoomNumber();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void SetRoomNumber(string number);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern double GetArea();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern double GetCeilingHeight();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void SetCeilingHeight(double height);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern bool GetShowLabels();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void SetShowLabels(bool show);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern Point3dCollection GetVertices();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void SetVerticesInternal(Point3dCollection vertices);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern int GetWindowDoorCount();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern Point3d GetCentroid();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void CalculateArea();

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void AddWindowDoorSymbol(WindowDoorType type, Point3d startPoint, Point3d endPoint, string name);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern void RemoveWindowDoorSymbol(int index);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern WindowDoorInfo GetWindowDoorSymbol(int index);

        [MethodImpl(MethodImplOptions.InternalCall)]
        private extern bool IsPointInsideRoom(Point3d point);

        #endregion

        #region Helper Methods

        private void CheckObjectState()
        {
            if (IsDisposed)
                throw new ObjectDisposedException(GetType().Name);
        }

        #endregion
    }
}
