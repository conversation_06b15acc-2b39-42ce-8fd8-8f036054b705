{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\RoomEntityManaged.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\RoomEntityManaged.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\RoomEntityManaged.csproj", "projectName": "RoomEntityManaged", "projectPath": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\RoomEntityManaged.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"System.Windows.Forms": {"target": "Package", "version": "[4.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}