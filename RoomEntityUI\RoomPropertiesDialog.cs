using System;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using RoomEntityManaged;

namespace RoomEntityUI
{
    /// <summary>
    /// Dialog for editing room properties
    /// </summary>
    public partial class RoomPropertiesDialog : Form
    {
        private RoomInfo _roomInfo;
        private bool _isMetricUnits = true;

        #region Controls

        private TableLayoutPanel tableLayoutPanel;
        private Label lblRoomName;
        private TextBox txtRoomName;
        private Label lblRoomNumber;
        private TextBox txtRoomNumber;
        private Label lblArea;
        private TextBox txtArea;
        private Label lblCeilingHeight;
        private TextBox txtCeilingHeight;
        private Label lblUnits;
        private ComboBox cmbUnits;
        private CheckBox chkShowLabels;
        private GroupBox grpWindowsDoors;
        private ListBox lstWindowsDoors;
        private Button btnAddWindow;
        private Button btnAddDoor;
        private Button btnRemoveSymbol;
        private Button btnOK;
        private Button btnCancel;
        private Button btnApply;

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets the room information
        /// </summary>
        public RoomInfo RoomInfo
        {
            get => _roomInfo;
            set
            {
                _roomInfo = value;
                UpdateControls();
            }
        }

        /// <summary>
        /// Gets whether metric units are being used
        /// </summary>
        public bool IsMetricUnits => _isMetricUnits;

        #endregion

        #region Constructor

        /// <summary>
        /// Default constructor
        /// </summary>
        public RoomPropertiesDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        /// <summary>
        /// Constructor with room information
        /// </summary>
        /// <param name="roomInfo">Room information to edit</param>
        public RoomPropertiesDialog(RoomInfo roomInfo) : this()
        {
            RoomInfo = roomInfo;
        }

        #endregion

        #region Initialization

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Room Properties";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // Main table layout
            tableLayoutPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 10,
                Padding = new Padding(10)
            };

            // Set column styles
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));

            // Room Name
            lblRoomName = new Label { Text = "Room Name:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtRoomName = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right };

            // Room Number
            lblRoomNumber = new Label { Text = "Room Number:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtRoomNumber = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right };

            // Area (read-only)
            lblArea = new Label { Text = "Area:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtArea = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right, ReadOnly = true, BackColor = SystemColors.Control };

            // Ceiling Height
            lblCeilingHeight = new Label { Text = "Ceiling Height:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtCeilingHeight = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right };

            // Units
            lblUnits = new Label { Text = "Units:", Anchor = AnchorStyles.Left, AutoSize = true };
            cmbUnits = new ComboBox 
            { 
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbUnits.Items.AddRange(new[] { "Metric (mm/m²)", "Imperial (in/ft²)" });
            cmbUnits.SelectedIndex = 0;

            // Show Labels
            chkShowLabels = new CheckBox 
            { 
                Text = "Show property labels", 
                Anchor = AnchorStyles.Left,
                AutoSize = true
            };

            // Windows/Doors group
            grpWindowsDoors = new GroupBox 
            { 
                Text = "Windows and Doors",
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                Height = 150
            };

            var windowDoorPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(5)
            };

            lstWindowsDoors = new ListBox 
            { 
                Dock = DockStyle.Fill,
                SelectionMode = SelectionMode.One
            };

            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                AutoSize = true
            };

            btnAddWindow = new Button { Text = "Add Window", Width = 100 };
            btnAddDoor = new Button { Text = "Add Door", Width = 100 };
            btnRemoveSymbol = new Button { Text = "Remove", Width = 100 };

            buttonPanel.Controls.AddRange(new Control[] { btnAddWindow, btnAddDoor, btnRemoveSymbol });

            windowDoorPanel.Controls.Add(lstWindowsDoors, 0, 0);
            windowDoorPanel.Controls.Add(buttonPanel, 1, 0);
            windowDoorPanel.SetRowSpan(lstWindowsDoors, 2);

            grpWindowsDoors.Controls.Add(windowDoorPanel);

            // Buttons
            var buttonLayoutPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                Height = 40,
                Padding = new Padding(10, 5, 10, 5)
            };

            btnCancel = new Button { Text = "Cancel", DialogResult = DialogResult.Cancel, Width = 75 };
            btnApply = new Button { Text = "Apply", Width = 75 };
            btnOK = new Button { Text = "OK", DialogResult = DialogResult.OK, Width = 75 };

            buttonLayoutPanel.Controls.AddRange(new Control[] { btnCancel, btnApply, btnOK });

            // Add controls to table layout
            tableLayoutPanel.Controls.Add(lblRoomName, 0, 0);
            tableLayoutPanel.Controls.Add(txtRoomName, 1, 0);
            tableLayoutPanel.Controls.Add(lblRoomNumber, 0, 1);
            tableLayoutPanel.Controls.Add(txtRoomNumber, 1, 1);
            tableLayoutPanel.Controls.Add(lblArea, 0, 2);
            tableLayoutPanel.Controls.Add(txtArea, 1, 2);
            tableLayoutPanel.Controls.Add(lblCeilingHeight, 0, 3);
            tableLayoutPanel.Controls.Add(txtCeilingHeight, 1, 3);
            tableLayoutPanel.Controls.Add(lblUnits, 0, 4);
            tableLayoutPanel.Controls.Add(cmbUnits, 1, 4);
            tableLayoutPanel.Controls.Add(chkShowLabels, 1, 5);
            tableLayoutPanel.Controls.Add(grpWindowsDoors, 0, 6);
            tableLayoutPanel.SetColumnSpan(grpWindowsDoors, 2);
            tableLayoutPanel.SetRowSpan(grpWindowsDoors, 3);

            // Add main controls to form
            this.Controls.Add(tableLayoutPanel);
            this.Controls.Add(buttonLayoutPanel);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeDialog()
        {
            // Event handlers
            cmbUnits.SelectedIndexChanged += CmbUnits_SelectedIndexChanged;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnApply.Click += BtnApply_Click;
            btnAddWindow.Click += BtnAddWindow_Click;
            btnAddDoor.Click += BtnAddDoor_Click;
            btnRemoveSymbol.Click += BtnRemoveSymbol_Click;
            lstWindowsDoors.SelectedIndexChanged += LstWindowsDoors_SelectedIndexChanged;

            // Validation
            txtRoomName.Validating += TxtRoomName_Validating;
            txtRoomNumber.Validating += TxtRoomNumber_Validating;
            txtCeilingHeight.Validating += TxtCeilingHeight_Validating;

            // Initialize with default values
            _roomInfo = new RoomInfo
            {
                Name = RoomConstants.DefaultRoomName,
                Number = RoomConstants.DefaultRoomNumber,
                CeilingHeight = RoomConstants.DefaultCeilingHeight,
                ShowLabels = true
            };

            UpdateControls();
        }

        #endregion

        #region Event Handlers

        private void CmbUnits_SelectedIndexChanged(object? sender, EventArgs e)
        {
            _isMetricUnits = cmbUnits.SelectedIndex == 0;
            UpdateAreaDisplay();
            UpdateCeilingHeightDisplay();
        }

        private void BtnOK_Click(object? sender, EventArgs e)
        {
            if (ValidateAndApplyChanges())
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnApply_Click(object? sender, EventArgs e)
        {
            ValidateAndApplyChanges();
        }

        private void BtnAddWindow_Click(object? sender, EventArgs e)
        {
            // This would open a dialog to get window parameters
            // For now, add a placeholder
            lstWindowsDoors.Items.Add("Window: New Window (1000mm)");
        }

        private void BtnAddDoor_Click(object? sender, EventArgs e)
        {
            // This would open a dialog to get door parameters
            // For now, add a placeholder
            lstWindowsDoors.Items.Add("Door: New Door (800mm)");
        }

        private void BtnRemoveSymbol_Click(object? sender, EventArgs e)
        {
            if (lstWindowsDoors.SelectedIndex >= 0)
            {
                lstWindowsDoors.Items.RemoveAt(lstWindowsDoors.SelectedIndex);
            }
        }

        private void LstWindowsDoors_SelectedIndexChanged(object? sender, EventArgs e)
        {
            btnRemoveSymbol.Enabled = lstWindowsDoors.SelectedIndex >= 0;
        }

        #endregion

        #region Validation

        private void TxtRoomName_Validating(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtRoomName.Text))
            {
                MessageBox.Show("Room name cannot be empty.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                e.Cancel = true;
            }
        }

        private void TxtRoomNumber_Validating(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtRoomNumber.Text))
            {
                MessageBox.Show("Room number cannot be empty.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                e.Cancel = true;
            }
        }

        private void TxtCeilingHeight_Validating(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            if (!double.TryParse(txtCeilingHeight.Text, out double height) || height <= 0)
            {
                MessageBox.Show("Please enter a valid ceiling height.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                e.Cancel = true;
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateControls()
        {
            if (_roomInfo == null) return;

            txtRoomName.Text = _roomInfo.Name;
            txtRoomNumber.Text = _roomInfo.Number;
            chkShowLabels.Checked = _roomInfo.ShowLabels;

            UpdateAreaDisplay();
            UpdateCeilingHeightDisplay();
            UpdateWindowDoorList();
        }

        private void UpdateAreaDisplay()
        {
            if (_roomInfo == null) return;

            if (_isMetricUnits)
            {
                txtArea.Text = $"{_roomInfo.AreaInSquareMeters:F2} m²";
            }
            else
            {
                double areaInSqFt = _roomInfo.AreaInSquareMeters * 10.764; // Convert m² to ft²
                txtArea.Text = $"{areaInSqFt:F2} ft²";
            }
        }

        private void UpdateCeilingHeightDisplay()
        {
            if (_roomInfo == null) return;

            if (_isMetricUnits)
            {
                txtCeilingHeight.Text = _roomInfo.CeilingHeightInMeters.ToString("F2", CultureInfo.InvariantCulture);
            }
            else
            {
                double heightInFt = _roomInfo.CeilingHeightInMeters * 3.28084; // Convert m to ft
                txtCeilingHeight.Text = heightInFt.ToString("F2", CultureInfo.InvariantCulture);
            }
        }

        private void UpdateWindowDoorList()
        {
            lstWindowsDoors.Items.Clear();
            // This would be populated from the actual room entity
            // For now, show placeholder data
            for (int i = 0; i < _roomInfo.WindowDoorCount; i++)
            {
                lstWindowsDoors.Items.Add($"Symbol {i + 1}");
            }
            
            btnRemoveSymbol.Enabled = false;
        }

        private bool ValidateAndApplyChanges()
        {
            try
            {
                // Validate room name
                if (string.IsNullOrWhiteSpace(txtRoomName.Text))
                {
                    MessageBox.Show("Room name cannot be empty.", "Validation Error");
                    txtRoomName.Focus();
                    return false;
                }

                // Validate room number
                if (string.IsNullOrWhiteSpace(txtRoomNumber.Text))
                {
                    MessageBox.Show("Room number cannot be empty.", "Validation Error");
                    txtRoomNumber.Focus();
                    return false;
                }

                // Validate ceiling height
                if (!double.TryParse(txtCeilingHeight.Text, out double height) || height <= 0)
                {
                    MessageBox.Show("Please enter a valid ceiling height.", "Validation Error");
                    txtCeilingHeight.Focus();
                    return false;
                }

                // Apply changes
                _roomInfo.Name = txtRoomName.Text.Trim();
                _roomInfo.Number = txtRoomNumber.Text.Trim();
                _roomInfo.ShowLabels = chkShowLabels.Checked;

                // Convert height based on units
                if (_isMetricUnits)
                {
                    _roomInfo.CeilingHeightInMeters = height;
                }
                else
                {
                    _roomInfo.CeilingHeightInMeters = height / 3.28084; // Convert ft to m
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying changes: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion
    }
}
