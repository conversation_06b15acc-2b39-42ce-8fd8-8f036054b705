using System;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using Autodesk.AutoCAD.Geometry;
using RoomEntityManaged;

namespace RoomEntityUI
{
    /// <summary>
    /// Dialog for adding/editing window and door symbols
    /// </summary>
    public partial class WindowDoorDialog : Form
    {
        private WindowDoorInfo _symbolInfo;

        #region Controls

        private TableLayoutPanel tableLayoutPanel;
        private Label lblType;
        private ComboBox cmbType;
        private Label lblName;
        private TextBox txtName;
        private Label lblStartPoint;
        private TextBox txtStartX;
        private TextBox txtStartY;
        private Label lblEndPoint;
        private TextBox txtEndX;
        private TextBox txtEndY;
        private Label lblWidth;
        private TextBox txtWidth;
        private Button btnOK;
        private Button btnCancel;

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets the window/door information
        /// </summary>
        public WindowDoorInfo SymbolInfo
        {
            get => _symbolInfo;
            set
            {
                _symbolInfo = value;
                UpdateControls();
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Default constructor
        /// </summary>
        public WindowDoorDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        /// <summary>
        /// Constructor with symbol information
        /// </summary>
        /// <param name="symbolInfo">Symbol information to edit</param>
        public WindowDoorDialog(WindowDoorInfo symbolInfo) : this()
        {
            SymbolInfo = symbolInfo;
        }

        /// <summary>
        /// Constructor for creating new symbol
        /// </summary>
        /// <param name="type">Type of symbol to create</param>
        public WindowDoorDialog(WindowDoorType type) : this()
        {
            _symbolInfo = new WindowDoorInfo
            {
                Type = type,
                Name = GetDefaultName(type),
                StartPoint = Point3d.Origin,
                EndPoint = new Point3d(1000, 0, 0) // Default 1m width
            };
            UpdateControls();
        }

        #endregion

        #region Initialization

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Window/Door Properties";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // Main table layout
            tableLayoutPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 7,
                Padding = new Padding(10)
            };

            // Set column styles
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F));

            // Type
            lblType = new Label { Text = "Type:", Anchor = AnchorStyles.Left, AutoSize = true };
            cmbType = new ComboBox 
            { 
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbType.Items.AddRange(new[] { "Window", "Door", "Sliding Door", "Double Door" });

            // Name
            lblName = new Label { Text = "Name:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtName = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right };

            // Start Point
            lblStartPoint = new Label { Text = "Start Point:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtStartX = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right, PlaceholderText = "X" };
            txtStartY = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right, PlaceholderText = "Y" };

            // End Point
            lblEndPoint = new Label { Text = "End Point:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtEndX = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right, PlaceholderText = "X" };
            txtEndY = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right, PlaceholderText = "Y" };

            // Width (calculated)
            lblWidth = new Label { Text = "Width:", Anchor = AnchorStyles.Left, AutoSize = true };
            txtWidth = new TextBox { Anchor = AnchorStyles.Left | AnchorStyles.Right, ReadOnly = true, BackColor = SystemColors.Control };

            // Buttons
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                Height = 40,
                Padding = new Padding(10, 5, 10, 5)
            };

            btnCancel = new Button { Text = "Cancel", DialogResult = DialogResult.Cancel, Width = 75 };
            btnOK = new Button { Text = "OK", DialogResult = DialogResult.OK, Width = 75 };

            buttonPanel.Controls.AddRange(new Control[] { btnCancel, btnOK });

            // Add controls to table layout
            tableLayoutPanel.Controls.Add(lblType, 0, 0);
            tableLayoutPanel.Controls.Add(cmbType, 1, 0);
            tableLayoutPanel.SetColumnSpan(cmbType, 2);

            tableLayoutPanel.Controls.Add(lblName, 0, 1);
            tableLayoutPanel.Controls.Add(txtName, 1, 1);
            tableLayoutPanel.SetColumnSpan(txtName, 2);

            tableLayoutPanel.Controls.Add(lblStartPoint, 0, 2);
            tableLayoutPanel.Controls.Add(txtStartX, 1, 2);
            tableLayoutPanel.Controls.Add(txtStartY, 2, 2);

            tableLayoutPanel.Controls.Add(lblEndPoint, 0, 3);
            tableLayoutPanel.Controls.Add(txtEndX, 1, 3);
            tableLayoutPanel.Controls.Add(txtEndY, 2, 3);

            tableLayoutPanel.Controls.Add(lblWidth, 0, 4);
            tableLayoutPanel.Controls.Add(txtWidth, 1, 4);
            tableLayoutPanel.SetColumnSpan(txtWidth, 2);

            // Add main controls to form
            this.Controls.Add(tableLayoutPanel);
            this.Controls.Add(buttonPanel);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeDialog()
        {
            // Event handlers
            cmbType.SelectedIndexChanged += CmbType_SelectedIndexChanged;
            txtStartX.TextChanged += CoordinateTextChanged;
            txtStartY.TextChanged += CoordinateTextChanged;
            txtEndX.TextChanged += CoordinateTextChanged;
            txtEndY.TextChanged += CoordinateTextChanged;
            btnOK.Click += BtnOK_Click;

            // Initialize with default values
            _symbolInfo = new WindowDoorInfo
            {
                Type = WindowDoorType.Window,
                Name = "Window",
                StartPoint = Point3d.Origin,
                EndPoint = new Point3d(1000, 0, 0)
            };

            UpdateControls();
        }

        #endregion

        #region Event Handlers

        private void CmbType_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_symbolInfo != null)
            {
                _symbolInfo.Type = (WindowDoorType)cmbType.SelectedIndex;
                if (string.IsNullOrEmpty(txtName.Text) || IsDefaultName(txtName.Text))
                {
                    txtName.Text = GetDefaultName(_symbolInfo.Type);
                }
            }
        }

        private void CoordinateTextChanged(object? sender, EventArgs e)
        {
            UpdateWidthDisplay();
        }

        private void BtnOK_Click(object? sender, EventArgs e)
        {
            if (ValidateAndApplyChanges())
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateControls()
        {
            if (_symbolInfo == null) return;

            cmbType.SelectedIndex = (int)_symbolInfo.Type;
            txtName.Text = _symbolInfo.Name;
            txtStartX.Text = _symbolInfo.StartPoint.X.ToString("F2", CultureInfo.InvariantCulture);
            txtStartY.Text = _symbolInfo.StartPoint.Y.ToString("F2", CultureInfo.InvariantCulture);
            txtEndX.Text = _symbolInfo.EndPoint.X.ToString("F2", CultureInfo.InvariantCulture);
            txtEndY.Text = _symbolInfo.EndPoint.Y.ToString("F2", CultureInfo.InvariantCulture);

            UpdateWidthDisplay();
        }

        private void UpdateWidthDisplay()
        {
            try
            {
                if (double.TryParse(txtStartX.Text, out double startX) &&
                    double.TryParse(txtStartY.Text, out double startY) &&
                    double.TryParse(txtEndX.Text, out double endX) &&
                    double.TryParse(txtEndY.Text, out double endY))
                {
                    var start = new Point3d(startX, startY, 0);
                    var end = new Point3d(endX, endY, 0);
                    double width = start.DistanceTo(end);
                    txtWidth.Text = $"{width:F2} mm ({width / 1000:F3} m)";
                }
                else
                {
                    txtWidth.Text = "Invalid coordinates";
                }
            }
            catch
            {
                txtWidth.Text = "Error calculating width";
            }
        }

        private bool ValidateAndApplyChanges()
        {
            try
            {
                // Validate name
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("Symbol name cannot be empty.", "Validation Error");
                    txtName.Focus();
                    return false;
                }

                // Validate coordinates
                if (!double.TryParse(txtStartX.Text, out double startX) ||
                    !double.TryParse(txtStartY.Text, out double startY) ||
                    !double.TryParse(txtEndX.Text, out double endX) ||
                    !double.TryParse(txtEndY.Text, out double endY))
                {
                    MessageBox.Show("Please enter valid coordinates.", "Validation Error");
                    return false;
                }

                // Check minimum width
                var start = new Point3d(startX, startY, 0);
                var end = new Point3d(endX, endY, 0);
                double width = start.DistanceTo(end);

                if (width < 100) // Minimum 100mm
                {
                    MessageBox.Show("Symbol width must be at least 100mm.", "Validation Error");
                    return false;
                }

                if (width > 5000) // Maximum 5m
                {
                    MessageBox.Show("Symbol width cannot exceed 5000mm.", "Validation Error");
                    return false;
                }

                // Apply changes
                _symbolInfo.Type = (WindowDoorType)cmbType.SelectedIndex;
                _symbolInfo.Name = txtName.Text.Trim();
                _symbolInfo.StartPoint = start;
                _symbolInfo.EndPoint = end;

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying changes: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private string GetDefaultName(WindowDoorType type)
        {
            return type switch
            {
                WindowDoorType.Window => "Window",
                WindowDoorType.Door => "Door",
                WindowDoorType.SlidingDoor => "Sliding Door",
                WindowDoorType.DoubleDoor => "Double Door",
                _ => "Symbol"
            };
        }

        private bool IsDefaultName(string name)
        {
            return name == "Window" || name == "Door" || name == "Sliding Door" || name == "Double Door" || name == "Symbol";
        }

        #endregion
    }
}
