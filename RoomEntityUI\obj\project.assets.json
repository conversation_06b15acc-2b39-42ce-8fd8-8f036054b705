{"version": 3, "targets": {".NETFramework,Version=v4.8": {"System.Windows.Forms/4.0.0": {"type": "package", "compile": {"lib/System.Windows.Forms.dll": {}}, "runtime": {"lib/System.Windows.Forms.dll": {}}}, "RoomEntityManaged/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.8", "dependencies": {"System.Windows.Forms": "4.0.0"}, "compile": {"bin/placeholder/RoomEntityManaged.dll": {}}, "runtime": {"bin/placeholder/RoomEntityManaged.dll": {}}}}}, "libraries": {"System.Windows.Forms/4.0.0": {"sha512": "WL3QPG1q88aIWO0JPOlkCeyNE+FK4ufSKY5j6J6qAFQyfCFiiOYxi8RAhev9H0CS/wmKbIK8qr8xZBod85oWKw==", "type": "package", "path": "system.windows.forms/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/System.Windows.Forms.dll", "system.windows.forms.4.0.0.nupkg.sha512", "system.windows.forms.nuspec"]}, "RoomEntityManaged/1.0.0": {"type": "project", "path": "../RoomEntityManaged/RoomEntityManaged.csproj", "msbuildProject": "../RoomEntityManaged/RoomEntityManaged.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["RoomEntityManaged >= 1.0.0", "System.Windows.Forms >= 4.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityUI\\RoomEntityUI.csproj", "projectName": "RoomEntityUI", "projectPath": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityUI\\RoomEntityUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\RoomEntityManaged.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (43)\\RoomEntityManaged\\RoomEntityManaged.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"System.Windows.Forms": {"target": "Package", "version": "[4.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}