# Build script for Room Entity AutoCAD plugin
# Requires Visual Studio 2022 and AutoCAD 2024

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Debug",
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean,
    
    [Parameter(Mandatory=$false)]
    [switch]$Rebuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$Deploy
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Define paths
$SolutionPath = "RoomEntity.sln"
$OutputPath = "bin\$Configuration"
$AutoCADPath = "C:\Program Files\Autodesk\AutoCAD 2024"
$ObjectARXPath = "C:\Autodesk\ObjectARX_for_AutoCAD_2024_Win_64bit_dlm"

# Check if required paths exist
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Path $AutoCADPath)) {
    Write-Error "AutoCAD 2024 not found at: $AutoCADPath"
    exit 1
}

if (-not (Test-Path $ObjectARXPath)) {
    Write-Error "ObjectARX SDK not found at: $ObjectARXPath"
    exit 1
}

# Set environment variables
Write-Host "Setting environment variables..." -ForegroundColor Yellow
$env:ACAD_2024 = $AutoCADPath
$env:ARXSDK_2024 = $ObjectARXPath

# Find MSBuild
$MSBuildPath = ""
$VSWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"

if (Test-Path $VSWhere) {
    $VSPath = & $VSWhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
    if ($VSPath) {
        $MSBuildPath = Join-Path $VSPath "MSBuild\Current\Bin\MSBuild.exe"
    }
}

if (-not (Test-Path $MSBuildPath)) {
    Write-Error "MSBuild not found. Please ensure Visual Studio 2022 is installed."
    exit 1
}

Write-Host "Using MSBuild: $MSBuildPath" -ForegroundColor Green

# Build parameters
$BuildParams = @(
    $SolutionPath
    "/p:Configuration=$Configuration"
    "/p:Platform=x64"
    "/p:WarningLevel=3"
    "/verbosity:minimal"
)

if ($Clean -or $Rebuild) {
    Write-Host "Cleaning solution..." -ForegroundColor Yellow
    $CleanParams = $BuildParams + "/t:Clean"
    & $MSBuildPath $CleanParams
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Clean failed with exit code: $LASTEXITCODE"
        exit $LASTEXITCODE
    }
}

if (-not $Clean) {
    Write-Host "Building solution..." -ForegroundColor Yellow
    
    if ($Rebuild) {
        $BuildParams += "/t:Rebuild"
    }
    
    & $MSBuildPath $BuildParams
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed with exit code: $LASTEXITCODE"
        exit $LASTEXITCODE
    }
    
    Write-Host "Build completed successfully!" -ForegroundColor Green
    
    # List output files
    if (Test-Path $OutputPath) {
        Write-Host "`nOutput files:" -ForegroundColor Yellow
        Get-ChildItem $OutputPath -Recurse -File | ForEach-Object {
            Write-Host "  $($_.FullName)" -ForegroundColor Gray
        }
    }
}

# Deploy if requested
if ($Deploy -and -not $Clean) {
    Write-Host "`nDeploying to AutoCAD..." -ForegroundColor Yellow
    
    $DeployPath = Join-Path $AutoCADPath "RoomEntity"
    
    if (-not (Test-Path $DeployPath)) {
        New-Item -ItemType Directory -Path $DeployPath -Force | Out-Null
    }
    
    # Copy ARX file
    $ArxFile = Join-Path $OutputPath "RoomEntityCpp.arx"
    if (Test-Path $ArxFile) {
        Copy-Item $ArxFile $DeployPath -Force
        Write-Host "  Copied: RoomEntityCpp.arx" -ForegroundColor Green
    }
    
    # Copy managed assemblies
    $ManagedFiles = @("RoomEntityManaged.dll", "RoomEntityUI.dll")
    foreach ($File in $ManagedFiles) {
        $FilePath = Join-Path $OutputPath $File
        if (Test-Path $FilePath) {
            Copy-Item $FilePath $DeployPath -Force
            Write-Host "  Copied: $File" -ForegroundColor Green
        }
    }
    
    Write-Host "`nDeployment completed to: $DeployPath" -ForegroundColor Green
    Write-Host "To load in AutoCAD, use: NETLOAD $DeployPath\RoomEntityCpp.arx" -ForegroundColor Cyan
}

Write-Host "`nBuild script completed!" -ForegroundColor Green
