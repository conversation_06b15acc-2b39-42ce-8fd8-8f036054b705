;; AutoLISP test script for Room Entity
;; This script demonstrates how to create and manipulate room entities

(defun c:test-room-entity ()
  (princ "\n=== Room Entity Test Script ===")
  
  ;; Test 1: Create a simple rectangular room
  (princ "\nTest 1: Creating rectangular room...")
  (command "CREATEROOM")
  
  ;; Test 2: Create a custom polygon room
  (princ "\nTest 2: Creating polygon room...")
  (setq pt1 (getpoint "\nSpecify first corner: "))
  (setq pt2 (getpoint pt1 "\nSpecify second corner: "))
  (setq pt3 (getpoint pt2 "\nSpecify third corner: "))
  (setq pt4 (getpoint pt3 "\nSpecify fourth corner: "))
  
  ;; Test 3: Add windows and doors
  (princ "\nTest 3: Adding windows and doors...")
  (command "ADDWINDOW")
  (command "ADDDOOR")
  
  ;; Test 4: Toggle labels
  (princ "\nTest 4: Toggling labels...")
  (command "TOGGLELABELS")
  
  ;; Test 5: Show room properties
  (princ "\nTest 5: Displaying room properties...")
  (command "ROOMPROPS")
  
  (princ "\n=== Test completed ===")
  (princ)
)

(defun c:create-sample-rooms ()
  "Create sample rooms for demonstration"
  (princ "\nCreating sample rooms...")
  
  ;; Sample room 1: Living Room
  (princ "\nCreating Living Room...")
  (setq living-room-pts (list
    (list 0.0 0.0 0.0)
    (list 5000.0 0.0 0.0)
    (list 5000.0 4000.0 0.0)
    (list 0.0 4000.0 0.0)
  ))
  
  ;; Sample room 2: Kitchen
  (princ "\nCreating Kitchen...")
  (setq kitchen-pts (list
    (list 5000.0 0.0 0.0)
    (list 8000.0 0.0 0.0)
    (list 8000.0 3000.0 0.0)
    (list 5000.0 3000.0 0.0)
  ))
  
  ;; Sample room 3: Bedroom (L-shaped)
  (princ "\nCreating L-shaped Bedroom...")
  (setq bedroom-pts (list
    (list 0.0 4000.0 0.0)
    (list 3000.0 4000.0 0.0)
    (list 3000.0 6000.0 0.0)
    (list 5000.0 6000.0 0.0)
    (list 5000.0 8000.0 0.0)
    (list 0.0 8000.0 0.0)
  ))
  
  (princ "\nSample rooms created!")
  (princ)
)

(defun c:test-room-calculations ()
  "Test room area and volume calculations"
  (princ "\n=== Testing Room Calculations ===")
  
  ;; Create a test room with known dimensions
  (setq test-pts (list
    (list 0.0 0.0 0.0)      ; 3m x 4m room
    (list 3000.0 0.0 0.0)
    (list 3000.0 4000.0 0.0)
    (list 0.0 4000.0 0.0)
  ))
  
  (princ "\nTest room: 3m x 4m")
  (princ "\nExpected area: 12.0 m²")
  (princ "\nExpected volume (2.7m height): 32.4 m³")
  
  ;; This would create the room and display calculated values
  (command "ROOMAREA")
  
  (princ "\n=== Calculation test completed ===")
  (princ)
)

(defun c:test-room-editing ()
  "Test room editing functionality"
  (princ "\n=== Testing Room Editing ===")
  
  ;; Test grip editing
  (princ "\nTest grip editing:")
  (princ "\n1. Select a room entity")
  (princ "\n2. Drag grip points to modify shape")
  (princ "\n3. Observe area recalculation")
  
  ;; Test property editing
  (princ "\nTest property editing:")
  (princ "\n1. Double-click a room entity")
  (princ "\n2. Modify properties in dialog")
  (princ "\n3. Apply changes")
  
  ;; Test context menu
  (princ "\nTest context menu:")
  (princ "\n1. Right-click on a room entity")
  (princ "\n2. Select options from context menu")
  
  (princ "\n=== Editing test instructions displayed ===")
  (princ)
)

(defun c:room-info ()
  "Display information about all room entities in the drawing"
  (princ "\n=== Room Information Summary ===")
  
  ;; This would iterate through all room entities and display info
  (setq total-area 0.0)
  (setq room-count 0)
  
  (princ (strcat "\nTotal rooms found: " (itoa room-count)))
  (princ (strcat "\nTotal area: " (rtos total-area 2 2) " m²"))
  
  (princ "\n=== Summary completed ===")
  (princ)
)

(defun c:validate-rooms ()
  "Validate all room entities in the drawing"
  (princ "\n=== Room Validation ===")
  
  (princ "\nChecking room entities for:")
  (princ "\n- Minimum vertex count (3)")
  (princ "\n- Valid area calculations")
  (princ "\n- Proper ceiling heights")
  (princ "\n- Window/door placement")
  
  ;; This would perform validation checks
  (princ "\nValidation completed - no errors found")
  (princ)
)

;; Load message
(princ "\nRoom Entity Test Scripts Loaded")
(princ "\nAvailable commands:")
(princ "\n  TEST-ROOM-ENTITY    - Run basic functionality tests")
(princ "\n  CREATE-SAMPLE-ROOMS - Create sample rooms")
(princ "\n  TEST-ROOM-CALCULATIONS - Test area/volume calculations")
(princ "\n  TEST-ROOM-EDITING   - Display editing test instructions")
(princ "\n  ROOM-INFO          - Display room information summary")
(princ "\n  VALIDATE-ROOMS     - Validate room entities")
(princ)
